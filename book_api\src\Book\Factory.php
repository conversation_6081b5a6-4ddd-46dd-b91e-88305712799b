<?php

namespace App\Book;

use App\Book\Book;
use App\HelperFunctions;
use App\Enums\Language;
use App\Enums\AgeGroup;
use App\Enums\Ethnicity;
use App\Enums\TitlePosition;

class Factory
{
    public static function getBook(string $bookName, Character $character): Book
    {
        $path = self::getBookPath($bookName);

        if (!file_exists($path)) {
            throw new \Exception('Book not found');
        }

        $items = self::getBookJson($path);

        /**
         * @var Scene[]
         */
        $scenes = [];
        foreach ($items as $scene) {
            $ageGroup = AgeGroup::getAgeGroupFromAge($character->age);
            $ethnicity = Ethnicity::getEthnicity($character->ethnicity);

            $text = self::getMatchingText(
                $scene['texts'],
                Language::ENGLISH,
                $ageGroup,
            );

            $prompt = self::getMatchingPrompt(
                $scene['prompts'],
                $character->gender === 'Boy' ? 'male' : 'female',
                $ageGroup,
                $ethnicity,
            );

            $ageGroupArray = $ageGroup === AgeGroup::ALL
                ? [1, 99]
                : explode('-', $ageGroup->value);

            $scenes[] = new Scene(
                $scene['id_scene'],
                $scene['is_cover'] > 0,
                $character->gender,
                $ageGroupArray,
                $ethnicity->value,

                $prompt['seed'],
                $text['text'],
                $prompt['prompt'],

                $scene['template_image'],
                $scene['character_image'],
                $scene['style_image'],
                $scene['overlap_image'],
                $scene['foreground_image'],

                $prompt['image_to_image'],
                $prompt['pose_reference'],
                $prompt['style_reference'],
                $prompt['content_reference'],
                $prompt['character_reference'],

                !empty($scene['overlap_image']),

                $prompt['image_to_image_strength'],
                $prompt['pose_reference_strength'],
                $prompt['style_reference_strength'],
                $prompt['content_reference_strength'],
                $prompt['character_reference_strength'],

                $scene['preset_style'],
                $scene['static_image'],

                $prompt['negative_prompt'],

                $scene['outpaint_prompt'],
                $scene['outpaint_top_size'],
                $scene['outpaint_bottom_size'],
                $scene['outpaint_left_size'],
                $scene['outpaint_right_size'],
                $scene['outpaint_seed'],
                $scene['outpaint_top_image'],
                $scene['outpaint_bottom_image'],
                $scene['outpaint_left_image'],
                $scene['outpaint_right_image'],
                $scene['clothes_top'],
                $scene['clothes_bottom'],
                ($text['title_position'])->value,
            );
        }

        $book = new Book();
        $book->setScenes($scenes);

        return $book;
    }

    /**
     * Returns the path to the book json file
     *
     * @param string $bookName
     * @return string
     */
    public static function getBookPath($bookName): string
    {
        return HelperFunctions::getRootDir() . '/books/' . $bookName . '.json';
    }

    /**
     * Returns the book json file as an array
     *
     * @param string $path
     * @return array{
     *      id_scene: int,
     *      is_cover: int,
     *      texts: array{
     *          text: string,
     *          language: Language,
     *          age_group: AgeGroup,
     *          title_position: TitlePosition,
     *      }[],
     *      prompts: array{
     *          seed: int,
     *          gender: string,
     *          age_group: AgeGroup,
     *          ethnicity: Ethnicity,
     *          prompt: string,
     *          negative_prompt: string,
     *          image_to_image: bool,
     *          pose_reference: bool,
     *          style_reference: bool,
     *          content_reference: bool,
     *          character_reference: bool,
     *          image_to_image_strength: string,
     *          pose_reference_strength: string,
     *          style_reference_strength: string,
     *          content_reference_strength: string,
     *          character_reference_strength: string,   
     *          is_default: bool,
     *      }[],
     *      template_image: string,
     *      character_image: string,
     *      style_image: string,
     *      overlap_image: string,
     *      static_image: string,
     *      foreground_image: string,
     *      preset_style: string,
     *      outpaint_prompt: string,
     *      outpaint_top_size: int,
     *      outpaint_bottom_size: int,
     *      outpaint_left_size: int,
     *      outpaint_right_size: int,
     *      outpaint_seed: int,
     *      outpaint_top_image: string,
     *      outpaint_bottom_image: string,
     *      outpaint_left_image: string,
     *      outpaint_right_image: string,
     *      clothes_top: string,
     *      clothes_bottom: string
     * }[]
     */
    public static function getBookJson($path): array
    {
        $scenes = json_decode(file_get_contents($path), true);

        $return = [];
        foreach ($scenes as $i => $scene) {
            $texts = [];
            $prompts = [];

            foreach ($scene['texts'] as $text) {
                $texts[] = [
                    'text' => $text['text'],
                    'language' => Language::getLanguage($text['language']),
                    'age_group' => AgeGroup::getAgeGroup($text['age_group']),
                    'title_position' => TitlePosition::getPosition((int) ($text['title_position'] ?? 1)),
                ];
            }

            foreach ($scene['prompts'] as $prompt) {
                $prompts[] = [
                    'seed' => (int) $prompt['seed'],
                    'gender' => $prompt['gender'],
                    'age_group' => AgeGroup::getAgeGroup($prompt['age_group']),
                    'ethnicity' => Ethnicity::getEthnicity($prompt['ethnicity']),
                    'prompt' => $prompt['prompt'],
                    'negative_prompt' => $prompt['negative_prompt'],
                    'image_to_image' => (bool) $prompt['image_to_image'],
                    'pose_reference' => (bool) $prompt['pose_reference'],
                    'style_reference' => (bool) $prompt['style_reference'],
                    'content_reference' => (bool) $prompt['content_reference'],
                    'character_reference' => (bool) $prompt['character_reference'],
                    'image_to_image_strength' => $prompt['image_to_image_strength'],
                    'pose_reference_strength' => $prompt['pose_reference_strength'],
                    'style_reference_strength' => $prompt['style_reference_strength'],
                    'content_reference_strength' => $prompt['content_reference_strength'],
                    'character_reference_strength' => $prompt['character_reference_strength'],
                    'is_default' => (bool) $prompt['is_default'],
                ];
            }

            $return[] = [
                'id_scene' => (int) $scene['id_scene'],
                'is_cover' => (int) ($scene['is_cover'] ?? 0),
                'texts' => $texts,
                'prompts' => $prompts,
                'template_image' => $scene['template_image'],
                'character_image' => $scene['character_image'],
                'style_image' => $scene['style_image'],
                'overlap_image' => $scene['overlap_image'],
                'foreground_image' => $scene['foreground_image'],
                'static_image' => $scene['static_image'],
                'preset_style' => $scene['preset_style'],
                'outpaint_prompt' => $scene['outpaint_prompt'],
                'outpaint_top_size' => (int) $scene['outpaint_top_size'],
                'outpaint_bottom_size' => (int) $scene['outpaint_bottom_size'],
                'outpaint_left_size' => (int) $scene['outpaint_left_size'],
                'outpaint_right_size' => (int) $scene['outpaint_right_size'],
                'outpaint_seed' => (int) $scene['outpaint_seed'],
                'outpaint_top_image' => $scene['outpaint_top_image'] ?? '',
                'outpaint_bottom_image' => $scene['outpaint_bottom_image'] ?? '',
                'outpaint_left_image' => $scene['outpaint_left_image'] ?? '',
                'outpaint_right_image' => $scene['outpaint_right_image'] ?? '',
                'clothes_top' => $scene['clothes_top'],
                'clothes_bottom' => $scene['clothes_bottom'],
            ];
        }

        return $return;
    }

    /**
     * Returns the correct matching text given the available texts, the expected
     * language, and the age group
     * 
     * @param array{
     *   text: string,
     *   language: Language,
     *   age_group: AgeGroup,
     *   title_position: TitlePosition
     * }[] $texts
     * @param Language $language
     * @param AgeGroup $ageGroup
     * @return array{
     *      text: string,
     *      language: Language,
     *      age_group: AgeGroup,
     *      title_position: TitlePosition
     * }
     */
    public static function getMatchingText(array $texts, Language $language, AgeGroup $ageGroup): array
    {
        if (!$texts) {
            return [
                'text' => '',
                'language' => $language,
                'age_group' => $ageGroup,
            ];
        }

        foreach ($texts as $text) {
            if (
                $text['language'] == $language &&
                $text['age_group'] == $ageGroup
            ) {
                return $text;
            }
        }

        /**
         * We cannot find one specific to that age group
         */
        foreach ($texts as $text) {
            if (
                $text['language'] == $language &&
                $text['age_group'] == AgeGroup::ALL
            ) {
                return $text;
            }
        }

        /**
         * We cannot find one specific to that language and age group
         */
        foreach ($texts as $text) {
            if (
                $text['language'] == Language::ENGLISH &&
                $text['age_group'] == AgeGroup::ALL
            ) {
                return $text;
            }
        }

        throw new \Exception('Text not found');
    }

    /**
     * Returns the correct matching text given the available prompts, the expected
     * gender, the age group, and the ethnicity
     * 
     * @param array{
     *      seed: int,
     *      gender: string,
     *      age_group: AgeGroup,
     *      ethnicity: Ethnicity,
     *      prompt: string,
     *      negative_prompt: string,
     *      image_to_image: bool,
     *      pose_reference: bool,
     *      style_reference: bool,
     *      content_reference: bool,
     *      character_reference: bool,
     *      image_to_image_strength: string,
     *      pose_reference_strength: string,
     *      style_reference_strength: string,
     *      content_reference_strength: string,
     *      character_reference_strength: string,   
     *      is_default: bool,
     * }[] $prompts
     * @param string $gender
     * @param AgeGroup $ageGroup
     * @param Ethnicity $ethnicity
     * 
     * @return array{
     *      seed: int,
     *      gender: string,
     *      age_group: AgeGroup,
     *      ethnicity: Ethnicity,
     *      prompt: string,
     *      negative_prompt: string,
     *      image_to_image: bool,
     *      pose_reference: bool,
     *      style_reference: bool,
     *      content_reference: bool,
     *      character_reference: bool,
     *      image_to_image_strength: string,
     *      pose_reference_strength: string,
     *      style_reference_strength: string,
     *      content_reference_strength: string,
     *      character_reference_strength: string,   
     *      is_default: bool,
     * }
     */
    public static function getMatchingPrompt(array $prompts, string $gender, AgeGroup $ageGroup, Ethnicity $ethnicity): array
    {
        if (!$prompts) {
            return [
                'seed' => 0,
                'gender' => $gender,
                'age_group' => $ageGroup,
                'ethnicity' => $ethnicity,
                'prompt' => '',
                'negative_prompt' => '',
                'image_to_image' => false,
                'pose_reference' => false,
                'style_reference' => false,
                'content_reference' => false,
                'character_reference' => false,
                'image_to_image_strength' => '',
                'pose_reference_strength' => '',
                'style_reference_strength' => '',
                'content_reference_strength' => '',
                'character_reference_strength' => '',
                'is_default' => false,
            ];
        }

        // Step 1: Filter prompts by gender, age group, and ethnicity
        $filteredPrompts = array_filter($prompts, function ($prompt) use ($gender, $ageGroup, $ethnicity) {
            return ($prompt['gender'] === $gender || $prompt['gender'] === 'all')
                && ($prompt['age_group'] === $ageGroup || $prompt['age_group'] === AgeGroup::ALL)
                && ($prompt['ethnicity'] === $ethnicity || $prompt['ethnicity'] === Ethnicity::ALL);
        });

        // Step 2: Sort by how well they match the criteria
        usort($filteredPrompts, function ($a, $b) use ($gender, $ageGroup, $ethnicity) {
            $score = fn($p) =>
                ($p['gender'] === $gender ? 1 : 0) +
                ($p['age_group'] === $ageGroup ? 1 : 0) +
                ($p['ethnicity'] === $ethnicity ? 1 : 0);

            return $score($b) <=> $score($a);
        });

        // Step 3: Reindex the array to fix reset() bug
        $filteredPrompts = array_values($filteredPrompts);

        // Step 4: Return the best match if available
        if (!empty($filteredPrompts)) {
            return $filteredPrompts[0];
        }

        // Step 5: Fallback to default prompt if no matches
        foreach ($prompts as $prompt) {
            if (!empty($prompt['is_default'])) {
                return $prompt;
            }
        }

        // Step 6: No valid prompt found
        throw new \Exception('Prompt not found and no default available');
    }
}
