<?php

namespace App\Book;

use App\Api\LeonardoClient;
use App\Entity\BookImage;
use App\Entity\BookOrder;
use App\HelperFunctions;
use App\Service\Image\OutpaintSettings;
use App\Service\Image\PostProcess;
use Doctrine\ORM\EntityManagerInterface;
use App\Book\Scene;

class Book
{
    /**
     * @var Scene[]
     */
    protected array $scenes;

    /**
     * Stores the character image to use for main character
     */
    protected Character|null $character = null;

    /**
     * Sets the scenes for the book
     * 
     * @param Scene[] $scenes
     */
    public function setScenes(array $scenes) {
        $this->scenes = $scenes;
    }

    /**
     * Returns all the scenes of a book as an array of Scenes
     * 
     * @return Scene[]
     */
    public function getScenes(): array {
        return $this->scenes;
    }

    /**
     * Sets the character image to use for main character
     * 
     * @param string $path
     */
    public function setCharacter(Character $character) {
        $this->character = $character;
    }

    /**
     * Request Image Generation for each scene and return generation ids asyncronously
     * 
     * @param int $age Age of the main character
     * @param string $gender Gender of the main character
     * @param boolean $isPreview If this is set to true, we will only generate the first scene
     * 
     * @return \Generator<Scene> Generation IDs that you can iterate on
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function requestGenerateImageAsync($age, $gender, $isPreview = false): \Generator {
        $client = new LeonardoClient();

        if (!$this->character) {
            // throw an error
            // The character image wasn't defined
            throw new \InvalidArgumentException('The character image wasn\'t defined');
        }

        $character = $this->character;

        foreach ($this->scenes as $i => $scene) {
            if ($scene->static_image) {
                continue;
            }

            $characterImage = isset($scene->character_image) && !empty($scene->character_image)
                ? $scene->character_image
                : $character->imagePath;

            $characterImageId = '';
            if ($characterImage) {
                $characterImageId = $client->uploadImage($characterImage);
            }

            $templateImageId = '';
            if (
                $scene->multi_characters &&
                $scene->overlap_image
            ) {
                // We use the overlap image for generation
                $templateImageId = $client->uploadImage($scene->overlap_image);
            } elseif ($scene->template_image) {
                $templateImageId = $client->uploadImage($scene->template_image);
            }

            $styleImageId = '';
            if ($scene->style_reference && $scene->style_image) {
                $styleImageId = $client->uploadImage($scene->style_image);
            }

            $generation = time();

            $generation = $scene->generateImage(
                $client,
                $characterImageId,
                $styleImageId,
                $templateImageId,
                $character,
                $isPreview
            );

            yield $generation;

            if ($isPreview) {
                break;
            }
        }
    }

    /**
     * Request Image Generation for each scene
     * 
     * @param int $age Age of the main character
     * @param string $gender Gender of the main character
     * @param boolean $isPreview If this is set to true, we will only generate the first scene
     * @param null|int $idScene Scene to generate. Set to null to generate all scenes
     * 
     * @return Scene[] Array of scenes
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function requestGenerateImage($age, $gender, $isPreview = false, $idScene = null): array {
        $client = new LeonardoClient();

        if (!$this->character) {
            // throw an error
            // The character image wasn't defined
            throw new \InvalidArgumentException('The character image wasn\'t defined');
        }

        $character = $this->character;

        /**
         * @var Scene[]
         */
        $generations = [];

        foreach ($this->scenes as $scene) {
            if ($scene->static_image) {
                continue;
            }

            if (
                !is_null($idScene) &&
                $idScene != $scene->idScene
            ) {
                continue;
            }

            $characterImage = isset($scene->character_image) && !empty($scene->character_image)
                ? $scene->character_image
                : $character->imagePath;

            $characterImageId = '';
            if ($characterImage) {
                $characterImageId = $client->uploadImage($characterImage);
            }

            $templateImageId = '';
            if (
                $scene->multi_characters &&
                $scene->overlap_image
            ) {
                // We use the overlap image for generation
                $templateImageId = $client->uploadImage($scene->overlap_image);
            } elseif ($scene->template_image) {
                $templateImageId = $client->uploadImage($scene->template_image);
            }

            $styleImageId = '';
            if ($scene->style_reference && $scene->style_image) {
                $styleImageId = $client->uploadImage($scene->style_image);
            }

            $generations[] = $scene->generateImage(
                $client,
                $characterImageId,
                $styleImageId,
                $templateImageId,
                $character,
                $isPreview
            );

            if ($isPreview) {
                break;
            }
        }

        return $generations;
    }

    /**
     * Save a given list of generation ids to the database
     * Use index as indication of sorting
     *
     * @param EntityManagerInterface $entityManager 
     * @param int $idCart
     * @param string $bookName
     * @param Scene[] $scenes
     * @param bool $isPreview
     *
     * @return void
     */
    public function saveGenerationIds(EntityManagerInterface $entityManager, int $idCart, string $bookName, array $scenes, $isPreview = false) {
        foreach ($scenes as $scene) {
            $this->saveGenerationId($entityManager, $idCart, $bookName, $scene, $isPreview);
        }
    }

    /**
     * Save a Generation ID to the database
     * 
     * @param EntityManagerInterface $entityManager 
     * @param int $idCart
     * @param string $bookName
     * @param Scene $scene
     * @param bool $isPreview
     *
     * @return void
     */
    public function saveGenerationId(EntityManagerInterface $entityManager, int $idCart, string $bookName, Scene $scene, $isPreview = false) {
        $entities = $entityManager->getRepository(BookImage::class)->findByGenerationId($scene->generationId);

        // Clear the images for the cart before saving
        // This is to avoid duplicates of the scene for the same cart
        $this->clearImages($entityManager, $idCart, $scene);

        $image = new BookImage();
        $image->setIdCart($idCart);
        $image->setBookName($bookName);
        $image->setGenerationId($scene->generationId);
        $image->setIsPreview($isPreview ? 1 : 0);
        $image->setIsCover($scene->isCover);

        if ($entities) {
            $existingImage = reset($entities);
            if($existingImage->getImagePath()){
                $image->setImagePath($existingImage->getImagePath());
            }
        }

        if ($scene->overlap_image) {
            // We need to save the template image so that we can overlap it with the generated image later
            $image->setTemplateImage($scene->template_image);
        }

        if ($scene->foreground_image) {
            // We need to save the foreground image so that we can overlap it over the generated image later
            $image->setForegroundImage($scene->foreground_image);
        }

        $outpaintSettings = (new OutpaintSettings(
            prompt: $scene->outpaint_prompt,
            top: $scene->outpaint_top_size,
            left: $scene->outpaint_left_size,
            seed: (int) $scene->outpaint_seed,
            right: $scene->outpaint_right_size,
            bottom: $scene->outpaint_bottom_size,

            topImage: isset($scene->outpaint_top_image) && $scene->outpaint_top_image && $scene->overlap_image
                ? $scene->outpaint_top_image
                : '',

            bottomImage: isset($scene->outpaint_bottom_image) && $scene->outpaint_bottom_image && $scene->overlap_image
                ? $scene->outpaint_bottom_image
                : '',

            leftImage: isset($scene->outpaint_left_image) && $scene->outpaint_left_image && $scene->overlap_image
                ? $scene->outpaint_left_image
                : '',

            rightImage: isset($scene->outpaint_right_image) && $scene->outpaint_right_image && $scene->overlap_image
                ? $scene->outpaint_right_image
                : '',
        ));

        $image->setOutpaintSettings($outpaintSettings);

        $entityManager->persist($image);
        $entityManager->flush();
    }

    /**
     * Saves the generated image in public/generations folder
     * Saves the path to that image in the database
     * 
     * @param EntityManagerInterface $entityManager
     * @param string $generationId
     * @param string $imagePath
     * 
     * @return BookImage
     */
    public static function saveGeneratedImage(EntityManagerInterface $entityManager, string $generationId, string $imagePath) {
        $entities = $entityManager->getRepository(BookImage::class)->findByGenerationId($generationId);
        $image = reset($entities);

        $imageName = basename($imagePath);
        $originalImageName = $imageName;

        $imagePath = HelperFunctions::getBookImageDir() . '/' . $imageName;

        $templateImage = $image->getTemplateImage();
        $foregroundImage = $image->getForegroundImage();
        if ($templateImage) {
            $imageName = 'transparent_' . $originalImageName;
            $outputPath = HelperFunctions::getBookImageDir() . '/' . $imageName;

            $imagePath = PostProcess::removeBg(
                $imagePath,
                $outputPath
            );

            if (!file_exists($imagePath)) {
                throw new \InvalidArgumentException('The transparent image couldn\'t be saved');
            }

            $imageName = 'overlap_' . $originalImageName;
            $outputPath = HelperFunctions::getBookImageDir() . '/' . $imageName;

            $imagePath = PostProcess::overlapTemplateImage(
                $imagePath,
                $templateImage,
                $outputPath
            );

            if ($foregroundImage) {
                $imageName = 'overlap2_' . $originalImageName;
                $outputPath = HelperFunctions::getBookImageDir() . '/' . $imageName;

                $imagePath = PostProcess::overlapTemplateImage(
                    $foregroundImage,
                    $imagePath,
                    $outputPath
                );
            }

            if (!file_exists($imagePath)) {
                throw new \InvalidArgumentException('The overlap image couldn\'t be saved');
            }
        }

        $outpaintSettings = $image->getOutpaintSettings();
        if ($outpaintSettings) {
            $imageName = 'outpaint_' . $originalImageName;
            $outputPath = HelperFunctions::getBookImageDir() . '/' . $imageName;

            $imagePath = PostProcess::outpaint(
                $imagePath,
                $outputPath,
                $outpaintSettings
            );
        }

        $image->setImagePath($imageName);

        $entityManager->persist($image);
        $entityManager->flush();

        return $image;
    }

    /**
     * Saves the generated image to public/generations folder
     * 
     * @param string $url
     * @param string $generationId
     *
     * @return string Name of the file the image is saved as
     */
    public static function saveImage(string $url, string $generationId) {
        $filename = md5($generationId) . '.jpg';
        $imagePath = HelperFunctions::getBookImageDir() . '/' . $filename;

        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->get($url);
            $image = $response->getBody()->getContents();

            file_put_contents($imagePath, $image);
        } catch (\Exception $e) {
            // throw an error
            // The image couldn't be saved
            throw new \InvalidArgumentException('The image couldn\'t be saved');
        }

        return $filename;
    }

    /**
     * Clears all images from the database for a given cart and scene
     * 
     * @param EntityManagerInterface $entityManager
     * @param int $idCart
     * @param Scene $scene
     * 
     * @return void
     */
    public function clearImages(EntityManagerInterface $entityManager, int $idCart, Scene $scene) {
        /**
         * @var BookImage[]
         */
        $images = $entityManager->getRepository(BookImage::class)->findByCartId($idCart);

        foreach ($images as $image) {
            if ($scene->generationId !== $image->getGenerationId()) {
                // Prevent deletion of images not related to this Generation ID
                continue;
            }

            $entityManager->remove($image);
        }

        $entityManager->flush();
    }

    /**
     * Adds a new entry in book order
     * 
     * @param EntityManagerInterface $entityManager
     * @param Character $character
     * @param string $bookName
     * @param int $idCart
     * @param string $name
     * @param string $photo
     *
     * @return BookOrder
     */
    public function addOrder(
        $entityManager,
        $character,
        $bookName,
        $idCart,
        $name,
        $photo
    ) {
        $orders = $entityManager->getRepository(BookOrder::class)->findByCartId($idCart);

        if ($orders) {
            foreach ($orders as $order) {
                $entityManager->remove($order);
            }

            $entityManager->flush();
        }

        $order = new BookOrder();
        $order->setAge($character->age);
        $order->setBookName($bookName);
        $order->setGender($character->gender);
        $order->setIdCart($idCart);
        $order->setName($name);
        $order->setCharacterPhoto($photo);
        $order->setEthnicity($character->ethnicity);
        $order->setHairStyle($character->hairStyle);
        $order->setHairLength($character->hairLength);

        $entityManager->persist($order);
        $entityManager->flush();

        return $order;
    }
}