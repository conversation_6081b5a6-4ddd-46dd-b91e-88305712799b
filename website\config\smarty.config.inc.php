<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

define('_PS_SMARTY_DIR_', _PS_TOOL_DIR_.'smarty/');

global $smarty;
$smarty = new SmartyCustom();
$smarty->setCompileDir(_PS_CACHE_DIR_.'smarty/compile');
$smarty->setCacheDir(_PS_CACHE_DIR_.'smarty/cache');
$smarty->use_sub_dirs = true; // Unused in community-theme-default.
$smarty->setConfigDir(_PS_SMARTY_DIR_.'configs');
$smarty->caching = false;
$smarty->force_compile = (Configuration::get('PS_SMARTY_FORCE_COMPILE') == _PS_SMARTY_FORCE_COMPILE_) ? true : false;
$smarty->compile_check = (Configuration::get('PS_SMARTY_FORCE_COMPILE') >= _PS_SMARTY_CHECK_COMPILE_) ? true : false;
$smarty->debug_tpl = _PS_ALL_THEMES_DIR_.'debug.tpl';

/* Use this constant if you want to load smarty without all PrestaShop functions */
if (defined('_PS_SMARTY_FAST_LOAD_') && _PS_SMARTY_FAST_LOAD_) {
    return;
}

if (defined('_PS_ADMIN_DIR_')) {
    require_once(dirname(__FILE__).'/smartyadmin.config.inc.php');
} else {
    require_once(dirname(__FILE__).'/smartyfront.config.inc.php');
}

smartyRegisterFunction($smarty, 'modifier', 'truncate', 'smarty_modifier_truncate');
smartyRegisterFunction($smarty, 'modifier', 'secureReferrer', ['Tools', 'secureReferrer']);

smartyRegisterFunction($smarty, 'function', 't', 'smartyTruncate'); // unused
smartyRegisterFunction($smarty, 'function', 'm', 'smartyMaxWords'); // unused
smartyRegisterFunction($smarty, 'function', 'p', 'smartyShowObject'); // Debug only
smartyRegisterFunction($smarty, 'function', 'd', 'smartyDieObject'); // Debug only
smartyRegisterFunction($smarty, 'function', 'l', 'smartyTranslate', false);
smartyRegisterFunction($smarty, 'function', 'hook', 'smartyHook');
smartyRegisterFunction($smarty, 'function', 'toolsConvertPrice', 'toolsConvertPrice');
smartyRegisterFunction($smarty, 'modifier', 'json_encode', ['Tools', 'jsonEncode']);
smartyRegisterFunction($smarty, 'modifier', 'json_decode', ['Tools', 'jsonDecode']);
smartyRegisterFunction($smarty, 'function', 'dateFormat', ['Tools', 'dateFormat']);
smartyRegisterFunction($smarty, 'function', 'convertPrice', ['Product', 'convertPrice']);
smartyRegisterFunction($smarty, 'function', 'convertPriceWithCurrency', ['Product', 'convertPriceWithCurrency']);
smartyRegisterFunction($smarty, 'function', 'displayWtPrice', ['Product', 'displayWtPrice']);
smartyRegisterFunction($smarty, 'function', 'displayWtPriceWithCurrency', ['Product', 'displayWtPriceWithCurrency']);
smartyRegisterFunction($smarty, 'function', 'displayPrice', ['Tools', 'displayPriceSmarty']);
smartyRegisterFunction($smarty, 'function', 'displayPriceValue', 'displayPriceValue');
smartyRegisterFunction($smarty, 'modifier', 'convertAndFormatPrice', ['Product', 'convertAndFormatPrice']); // used twice
smartyRegisterFunction($smarty, 'function', 'getAdminToken', ['Tools', 'getAdminTokenLiteSmarty']);
smartyRegisterFunction($smarty, 'function', 'displayAddressDetail', ['AddressFormat', 'generateAddressSmarty']);
smartyRegisterFunction($smarty, 'function', 'getWidthSize', ['Image', 'getWidth']);
smartyRegisterFunction($smarty, 'function', 'getHeightSize', ['Image', 'getHeight']);
smartyRegisterFunction($smarty, 'function', 'addJsDef', ['Media', 'addJsDef']);
smartyRegisterFunction($smarty, 'block', 'addJsDefL', ['Media', 'addJsDefL']);
smartyRegisterFunction($smarty, 'modifier', 'boolval', ['Tools', 'boolval']);
smartyRegisterFunction($smarty, 'modifier', 'cleanHtml', 'smartyCleanHtml');
smartyRegisterFunction($smarty, 'function', 'implode', array('Tools', 'smartyImplode'));
smartyRegisterFunction($smarty, 'modifier', 'utf8ToIdn', array('Tools', 'convertEmailToIdn'));
smartyRegisterFunction($smarty, 'modifier', 'idnToUtf8', array('Tools', 'convertEmailFromIdn'));

function smartyDieObject($params, $smarty)
{
    return Tools::d($params['var']);
}

function smartyShowObject($params, $smarty)
{
    return Tools::p($params['var']);
}

function smartyMaxWords($params, $smarty)
{
    Tools::displayAsDeprecated();
    $params['s'] = str_replace('...', ' ...', html_entity_decode($params['s'], ENT_QUOTES, 'UTF-8'));
    $words = explode(' ', $params['s']);

    foreach ($words as &$word) {
        if (Tools::strlen($word) > $params['n']) {
            $word = Tools::substr(trim(chunk_split($word, $params['n']-1, '- ')), 0, -1);
        }
    }

    return implode(' ',  Tools::htmlentitiesUTF8($words));
}

function smartyTruncate($params, $smarty)
{
    Tools::displayAsDeprecated();
    $text = isset($params['strip']) ? strip_tags($params['text']) : $params['text'];
    $length = $params['length'];
    $sep = isset($params['sep']) ? $params['sep'] : '...';

    if (Tools::strlen($text) > $length + Tools::strlen($sep)) {
        $text = Tools::substr($text, 0, $length).$sep;
    }

    return (isset($params['encode']) ? Tools::htmlentitiesUTF8($text, ENT_NOQUOTES) : $text);
}

function smarty_modifier_truncate($string, $length = 80, $etc = '...', $break_words = false, $middle = false, $charset = 'UTF-8')
{
    if (!$length) {
        return '';
    }

    $string = trim($string);

    if (Tools::strlen($string) > $length) {
        $length -= min($length, Tools::strlen($etc));
        if (!$break_words && !$middle) {
            $string = preg_replace('/\s+?(\S+)?$/u', '', Tools::substr($string, 0, $length+1, $charset));
        }
        return !$middle ? Tools::substr($string, 0, $length, $charset).$etc : Tools::substr($string, 0, $length/2, $charset).$etc.Tools::substr($string, -$length/2, $length, $charset);
    } else {
        return $string;
    }
}

function smarty_modifier_htmlentitiesUTF8($string)
{
    return Tools::htmlentitiesUTF8($string);
}
function smartyMinifyHTML($tpl_output, $smarty)
{
    $context = Context::getContext();
    if (isset($context->controller) && in_array($context->controller->php_self, ['pdf-invoice', 'pdf-order-return', 'pdf-order-slip'])) {
        return $tpl_output;
    }
    $tpl_output = Media::minifyHTML($tpl_output);
    return $tpl_output;
}

function smartyPackJSinHTML($tpl_output, $smarty)
{
    $context = Context::getContext();
    if (isset($context->controller) && in_array($context->controller->php_self, ['pdf-invoice', 'pdf-order-return', 'pdf-order-slip'])) {
        return $tpl_output;
    }
    $tpl_output = Media::packJSinHTML($tpl_output);
    return $tpl_output;
}

function smartyRegisterFunction($smarty, $type, $function, $params, $lazy = true)
{
    if (!in_array($type, ['function', 'modifier', 'block'])) {
        return false;
    }

    // lazy is better if the function is not called on every page
    if ($lazy && is_array($params)) {
        $name = $params[1];
        $lazy_register = SmartyLazyRegister::getInstance();
        $lazy_register->register($name, $type, $params);

        // SmartyLazyRegister allows to only load external class when they are needed
        $smarty->registerPlugin($type, $function, [$lazy_register, $name]);
    } else {
        $smarty->registerPlugin($type, $function, $params);
    }
}

function smartyHook($params, $smarty)
{
    if (!empty($params['h'])) {
        $id_module = null;
        $hook_params = $params;
        $hook_params['smarty'] = $smarty;
        if (!empty($params['mod'])) {
            $module = Module::getInstanceByName($params['mod']);
            if ($module && $module->id) {
                $id_module = $module->id;
            } else {
                return '';
            }
            unset($hook_params['mod']);
        }
        unset($hook_params['h']);
        return Hook::exec($params['h'], $hook_params, $id_module);
    }
}

function smartyCleanHtml($data)
{
    // Prevent xss injection.
    if (Validate::isCleanHtml($data)) {
        return $data;
    }
}

function toolsConvertPrice($params, $smarty)
{
    return Tools::convertPrice($params['price'], Context::getContext()->currency);
}

/**
 * Convert a price for display in an input field in back office. This means,
 * allow full precision of _TB_PRICE_DATABASE_PRECISION_, but reduce the number
 * of trailing zeros beyond PS_PRICE_DISPLAY_PRECISION. This should give the
 * nicest display possible.
 *
 * Formatting should match JavaScript function displayPriceValue (in admin.js).
 * Which means: don't forget to transport any changes made here to there.
 *
 * @param float|string $params['price'] Raw price in context currency.
 * @param float|string $smarty          Unused.
 *
 * @return string Price prettified, without currency sign.
 *
 * @since 1.1.0
 */
function displayPriceValue($params, $smarty)
{
    $displayDecimals = 0;
    if (Context::getContext()->currency->decimals) {
        $displayDecimals = Configuration::get('PS_PRICE_DISPLAY_PRECISION');
    }

    // String -> float -> string gets rid of trailing zeros.
    $price = (float) $params['price'];
    // No need for the more expensive Tools::ps_round() here.
    if ((string) $price === (string) round($price, $displayDecimals)) {
        // Price more rounded than display precision.
        $formatted = number_format($price, $displayDecimals, '.', '');
    } else {
        // Show full precision.
        $formatted = (string) $price;
    }

    return $formatted;
}

/**
 * Used to delay loading of external classes with smarty->register_plugin
 */
class SmartyLazyRegister
{
    protected $registry = [];
    protected static $instance;

    /**
     * Register a function or method to be dynamically called later
     * @param string|array $params function name or array(object name, method name)
     */
    public function register($name, $type = 'function', $callable = null)
    {
        if (is_null($callable)) {
            if (is_array($name) && count($name) === 2) {
                $callable = $name;
                $name = $name[1];
            } else {
                throw new PrestaShopException('Invalid usage of SmartyLazyRegister::register');
            }
        }

        $this->registry[$name] = [
            'callable' => $callable,
            'type' => $type
        ];
    }

    /**
     * Dynamically call static function or method
     *
     * @param string $name function name
     * @param mixed $arguments function argument
     * @return mixed function return
     */
    public function __call($name, $arguments)
    {
        $item = $this->registry[$name];
        $callable = $item['callable'];
        $type = $item['type'];
        if ($type === 'block') {
            // signature of smarty block plugin is: function($params, $content, $template, &$repeat)
            // we need to pass 4th parameter as reference
            return call_user_func_array($callable, [$arguments[0], $arguments[1], $arguments[2], &$arguments[3]]);
        } else {
            // signature of smarty function plugin is: function($params, $smarty)
            // signature of smarty function plugin is: function modifier($value, [$param1, $param2, $param3])
            // there are no references, we can simply forward the call with input arguments
            return call_user_func_array($callable, $arguments);
        }
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new SmartyLazyRegister();
        }
        return self::$instance;
    }
}
