<?php

namespace App\Service;

use App\Book\Character;
use App\Entity\BookOrder;
use App\HelperFunctions;
use Mpdf\Mpdf;
use App\Book\Factory as BookFactory;
use App\Enums\TitlePosition;
use Mpdf\Output\Destination;

class BookGenerator
{
    public int $idCart;
    public string $bookName;
    public string $name;
    public int $age;
    public string $gender;

    /**
     * @var string[]
     */
    public array $images;

    public function __construct(
        private BookOrder $order,
        private int $timespent,
        private int $timeout
    ) {
    }

    public function setIdCart(int $idCart) {
        $this->idCart = $idCart;
    }

    public function getIdCart() {
        return $this->idCart;
    }

    public function setBookName(string $bookName) {
        $this->bookName = $bookName;
    }

    public function getBookName() {
        return $this->bookName;
    }

    public function setName(string $name) {
        $this->name = $name;
    }

    public function getName() {
        return $this->name;
    }

    public function setAge(int $age) {
        $this->age = $age;
    }

    public function getAge() {
        return $this->age;
    }

    public function setGender(string $gender) {
        $this->gender = $gender;
    }

    public function getGender() {
        return $this->gender;
    }

    public function setImages(array $images) {
        $this->images = $images;
    }

    public function getImages() {
        return $this->images;
    }

    /**
     * Generates a book and returns the path to the generated book
     * 
     * @return false|string
     */
    public function createBook() {
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $mpdf = new Mpdf([
            // Define the page size in mm
            'format' => [
                210,
                297
            ],

            // Define fonts
            'fontDir' => array_merge($fontDirs, [HelperFunctions::getRootDir() . '/fonts']),
            'fontdata' => [
                ...$fontData,
                'playfair' => [
                    'R' => 'PlayfairDisplay-VariableFont_wght.ttf',
                    'B' => 'PlayfairDisplay-VariableFont_wght.ttf',
                    'I' => 'PlayfairDisplay-VariableFont_wght.ttf',
                    'BI' => 'PlayfairDisplay-VariableFont_wght.ttf',
                ]
            ],

            // Set the default font
            'default_font' => 'playfair',
        ]);

        // Get page dimensions
        $pageWidth = $mpdf->w;   // Page width in mm
        $pageHeight = $mpdf->h;  // Page height in mm

        $filepath = HelperFunctions::getUploadsDir() . '/' . $this->order->getCharacterPhoto();

        $character = new Character();
        $character->age = $this->order->getAge();
        $character->gender = $this->order->getGender();
        $character->useImage($filepath);

        $book = BookFactory::getBook(
            $this->getBookName(),
            $character
        );

        $scenes = $book->getScenes();
        $images = $this->getImages();

        $imageIndex = 0;
        $start = time();

        foreach ($scenes as $i => $scene) {
            $timespent = (time() - $start) + $this->timespent;

            if ($timespent > $this->timeout) {
                return false;
            }

            $imageToUse = $images[$imageIndex] ?? '';

            if (isset($scene->static_image) && $scene->static_image) {
                $imageToUse = $scene->static_image;
            }

            if (!$imageToUse) {
                continue;
            }

            if (isset($scene->static_image) && $scene->static_image) {
                $imagePath = $imageToUse;
            } else {
                $imagePath = HelperFunctions::getBookImageDir() . '/' . basename($images[$imageIndex]);
                $imageIndex++;
            }

            $mpdf->AddPage();

            $mpdf->Image(
                $imagePath,
                0,
                0,
                $pageWidth,
                $pageHeight,
                'jpg',
                '',
                true,
                false
            );

            if (isset($scene->text) && $scene->text) {
                $pagePtHeight = $mpdf->hPt;
                $pagePtWidth = $mpdf->wPt;

                if (!$scene->isCover) {
                    $mpdf->AddPage();
                }

                $fontSize = 19;

                if ($scene->isCover) {
                    $fontSize = 27;
                } elseif ($this->getAge() > 5) {
                    $fontSize = 17;
                } elseif ($this->getAge() > 5) {
                    $fontSize = 14;
                }

                $pronoun1 = $this->getGender() === 'Girl' ? "she" : "he";
                $pronoun2 = $this->getGender() === 'Girl' ? "her" : "his";
                $pronoun3 = $this->getGender() === 'Girl' ? "her" : "him";
                $pronoun4 = $this->getGender() === 'Girl' ? "She" : "He";
                $pronoun5 = $this->getGender() === 'Girl' ? "Her" : "His";

                $text = str_replace(PHP_EOL, '<br/>', $scene->text);
                $text = str_replace("{AGE}", $this->getAge(), $text);
                $text = str_replace("{NAME}", $this->getName(), $text);
                $text = str_replace("{PRONOUN1}", $pronoun1, $text);
                $text = str_replace("{PRONOUN2}", $pronoun2, $text);
                $text = str_replace("{PRONOUN3}", $pronoun3, $text);
                $text = str_replace("{PRONOUN4}", $pronoun4, $text);
                $text = str_replace("{PRONOUN5}", $pronoun5, $text);

                if ($scene->isCover) {
                    $top = 'auto';
                    $bottom = 'auto';
                    $titlePosition = TitlePosition::getPosition($scene->title_position);

                    if ($titlePosition === TitlePosition::TOP) {
                        $top = ($pagePtHeight * 0.11) . 'pt';
                    } elseif ($titlePosition === TitlePosition::BOTTOM) {
                        $bottom = ($pagePtHeight * 0.11) . 'pt';
                    }

                    // White Overlay
                    $html = '<div style="width: ' . $pagePtWidth . 'pt; height: ' . $pagePtHeight . 'pt; position: absolute; top: 0; left: 0; background-color: rgba(255, 255, 255, 0.2)">&nbsp;</div>';

                    // Cover Title
                    $html .= '<div style="font-size: ' . $fontSize . '; position:absolute; top: ' . $top . '; left:0; right: 0; bottom: ' . $bottom . '; text-align: center; margin: auto;">';
                        $html .= '<div style="width: 80%; margin:0 auto; min-height: ' . ($pagePtHeight * 0.12) . 'pt; background-color: rgba(165, 91, 75, 0.8); padding: 1%; color: white">';
                            $html .= $text;
                        $html .= '</div>';
                    $html .= '</div>';
                } else {
                    $html = '<table style="width: ' . $pagePtWidth . 'pt; font-size: ' . $fontSize . 'px; margin: 0; padding: 0;" cellpadding="0" cellspacing="0">';
                    $html .= '<tr>';
                    $html .= '<td style="height: ' . $pagePtHeight . 'pt; text-align: center; vertical-align: middle; padding: 0px 5px; margin: 0;">';
                    $html .= $text;
                    $html .= '</td>';
                    $html .= '</tr>';
                    $html .= '</table>';
                }

                $mpdf->WriteHTML($html);
            }
        }

        /**
         * Start adding logo
         */
        $mpdf->AddPage();
        $imageWidth = 105;
        $imageHeight = 34;
        $mpdf->Image(
            '/home/<USER>/public_html/website/img/stories/logo-1.png',
            ($pageWidth - $imageWidth) / 2,
            ($pageHeight - $imageHeight) / 2,
            $imageWidth,
            $imageHeight
        );

        $filepath = BookGenerator::getPdfPath($this->getIdCart());
        $mpdf->Output($filepath, Destination::INLINE);

        exit;

        // set permission to 644
        chmod($filepath, 0644);

        return $filepath;
    }

    public static function getPdfPath(int $idCart) {
        return HelperFunctions::getRootDir() . '/pdfs/order-' . $idCart . '.pdf';
    }
}
