<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminMetaControllerCore
 *
 * @since 1.0.0
 */
class AdminUpdatesControllerCore extends AdminController
{
    // @codingStandardsIgnoreStart
    public $table = 'update';
    public $className = 'GitUpdate';
    public $lang = true;

    /** @var ShopUrl */
    protected $url = false;
    protected $toolbar_scroll = false;
    protected $ht_file = '';
    protected $rb_file = '';
    protected $rb_data = [];
    protected $sm_file = '';
    /** @var Meta $object */
    protected $object;
    // @codingStandardsIgnoreEnd

    /**
     * AdminMetaControllerCore constructor.
     *
     * @since 1.0.0
     */
    public function __construct()
    {
        $this->bootstrap = true;
        $this->identifier_name = 'page';

        parent::__construct();
    }

    public function initContent () {

        $changelog = GitUpdate::getChangeLog();

        $canRollback = (bool) Configuration::get('STM_PREVIOUS_BRANCH', false);

        $updateStatus = (int) Configuration::get('STM_UPDATE_STATUS', 0);

        $this->context->smarty->assign(
            [
                'has_updates' => (bool) $this->hasSystemUpdates(),
                'updateStatus' => $updateStatus,
                'changelog' => $changelog,
                'canRollback' => $canRollback,
                'rollback_url' => $this->context->link->getAdminLink('AdminUpdates') . '&rollback=1'
            ]
        );

        parent::initContent();

    }

    public function postProcess () {
        if (Tools::isSubmit('applySystemUpdates')) {
            $updateStatus = (int) $this->context->shop->applySystemUpdate();

            if ($updateStatus === 0) {
                $this->changeToLatestVersion();
            }

            Configuration::updateValue('STM_UPDATE_STATUS', $updateStatus);

            header('Location: ' . $this->context->link->getAdminLink('AdminUpdates'));
            exit;
        }

        if(Tools::isSubmit('rollback')) {

            $repo = new GitUpdate(_PS_ROOT_DIR_);
    
            $updatesFetched = $repo->rollback();

            $this->changeToPreviousVersion();

            Configuration::updateValue('STM_UPDATE_STATUS', 1);

            header('Location: ' . $this->context->link->getAdminLink('AdminUpdates'));

        }

    }

    private function hasSystemUpdates () : bool {

        $latestVersion = (string) GitUpdate::getLatestVersion();

        $hasUpdates = version_compare($latestVersion, _STM_VERSION_);

        return $hasUpdates > 0;

    }

    private function changeToLatestVersion () {

        $latestVersion = (string) GitUpdate::getLatestVersion();

        if ($latestVersion) {
            $this->changeVersionInSettingsFile($latestVersion);
        }

    }

    private function changeToPreviousVersion () {

        $previousVersion = Configuration::get('STM_PREVIOUS_SYSTEM_VERSION', '');

        if ($previousVersion) {
            $this->changeVersionInSettingsFile($previousVersion);
        }

    }

    private function changeVersionInSettingsFile (string $version) {

        $currentVersion = _STM_VERSION_;
        $settingsFile = _PS_ROOT_DIR_ . '/config/settings.inc.php';

        $settings = Tools::file_get_contents($settingsFile);

        $settings = str_replace($currentVersion, $version, $settings);

        file_put_contents($settingsFile, $settings);

    }
}
