<?php

namespace App\Entity;

use App\Repository\BookImageRepository;
use App\Service\Image\OutpaintSettings;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BookImageRepository::class)]
class BookImage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $book_name = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $image_path = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $generation_id = null;

    #[ORM\Column]
    private ?int $id_cart = null;

    #[ORM\Column(type: Types::TEXT, nullable: true, options: ["default" => null])]
    private ?string $template_image = null;

    #[ORM\Column(type: Types::TEXT, nullable: true, options: ["default" => null])]
    private ?string $foreground_image = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $outpaint_settings = null;

    #[ORM\Column]
    private ?int $is_preview = null;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $is_processing = false;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $is_cover = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getBookName(): ?string
    {
        return $this->book_name;
    }

    public function setBookName(string $book_name): static
    {
        $this->book_name = $book_name;

        return $this;
    }

    public function getImagePath(): ?string
    {
        return $this->image_path;
    }

    public function setImagePath(string $image_path): static
    {
        $this->image_path = $image_path;

        return $this;
    }

    public function getGenerationId(): ?string
    {
        return $this->generation_id;
    }

    public function setGenerationId(string $generation_id): static
    {
        $this->generation_id = $generation_id;

        return $this;
    }

    public function getIdCart(): ?int
    {
        return $this->id_cart;
    }

    public function setIdCart(int $id_cart): static
    {
        $this->id_cart = $id_cart;

        return $this;
    }

    public function getTemplateImage(): ?string
    {
        return $this->template_image;
    }

    public function setTemplateImage(string $template_image): static
    {
        $this->template_image = $template_image;

        return $this;
    }

    public function getForegroundImage(): ?string
    {
        return $this->foreground_image;
    }

    public function setForegroundImage(string $foreground_image): static
    {
        $this->foreground_image = $foreground_image;

        return $this;
    }

    public function getOutpaintSettings(): ?OutpaintSettings
    {
        $outpaint_settings = new OutpaintSettings(
            prompt: $this->outpaint_settings['prompt'],
            top: $this->outpaint_settings['top'],
            left: $this->outpaint_settings['left'],
            seed: $this->outpaint_settings['seed'],
            right: $this->outpaint_settings['right'],
            bottom: $this->outpaint_settings['bottom'],
            topImage: $this->outpaint_settings['topImage'] ?? '',
            bottomImage: $this->outpaint_settings['bottomImage'] ?? '',
            leftImage: $this->outpaint_settings['leftImage'] ?? '',
            rightImage: $this->outpaint_settings['rightImage'] ?? '',
        );

        return $outpaint_settings;
    }

    public function setOutpaintSettings(OutpaintSettings $outpaint_settings): static
    {
        $outpaint_settings = json_encode($outpaint_settings);
        $outpaint_settings = json_decode($outpaint_settings, true);

        $this->outpaint_settings = $outpaint_settings;

        return $this;
    }

    public function getIsPreview(): ?int
    {
        return $this->is_preview;
    }

    public function setIsPreview(int $is_preview): static
    {
        $this->is_preview = $is_preview;

        return $this;
    }

    public function getIsProcessing(): ?bool
    {
        return $this->is_processing;
    }

    public function setIsProcessing(bool $is_processing): static
    {
        $this->is_processing = $is_processing;

        return $this;
    }

    public function getIsCover(): ?bool
    {
        return $this->is_cover;
    }

    public function setIsCover(bool $is_cover): static
    {
        $this->is_cover = $is_cover;

        return $this;
    }
}
