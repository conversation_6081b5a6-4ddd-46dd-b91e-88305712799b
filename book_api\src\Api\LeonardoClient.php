<?php

namespace App\Api;

use App\Api\APIClientInterface;
use GuzzleHttp\Client;

class LeonardoClient implements APIClientInterface
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://cloud.leonardo.ai/api/rest/v1/',

            'headers' => [
                'Authorization' => 'Bearer ' . $_ENV['LEONARDO_API_KEY'],
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ]
        ]);
    }

    public function get(string $endpoint, array $params = []): array
    {
        $response = $this->client->get($endpoint, $params);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function post(string $endpoint, array $params = []): array
    {
        $response = $this->client->post($endpoint, $params);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function put(string $endpoint, array $params = []): array
    {
        $response = $this->client->put($endpoint, $params);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function delete(string $endpoint, array $params = []): array
    {
        $response = $this->client->delete($endpoint, $params);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function uploadImage($image): string
    {
        $imageType = pathinfo($image, PATHINFO_EXTENSION);
        $initImage = $this->initImage($imageType);

        $uploadClient = new Client();

        $uploadEndpoint = $initImage['uploadInitImage']['url'];
        $uploadFields = json_decode($initImage['uploadInitImage']['fields'], true);

        $uploadClient->post(
            $uploadEndpoint,
            [
                'multipart' => array_merge(
                    array_map(
                        function ($key, $value) {
                            return [
                                'name'     => $key,
                                'contents' => $value
                            ];
                        },

                        array_keys($uploadFields),

                        $uploadFields
                    ),

                    [
                        [
                            'name' => 'file',
                            'contents' => fopen($image, 'r'),
                        ],
                    ]
                ),

                'headers' => [
                    'Content-Length' => 'auto'  // Let Guzzle set it automatically
                ]
            ]
        );

        return $initImage['uploadInitImage']['id'];
    }

    protected function initImage($type): mixed
    {
        $response = $this->post(
            'init-image',

            [
                'body' => '{"extension":"' . $type . '"}',
            ]
        );

        return $response;
    }
}