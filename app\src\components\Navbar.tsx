// components/Navbar.jsx
"use client";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { getAssetUrl } from "@/tools";

const Navbar = () => {
    const [isOpen, setIsOpen] = useState(false);

    const menu = [
        {
            label: 'Kategorier',
            link: '/',
        },
        {
            label: 'Sådan virker det',
            link: '/',
        },
        {
            label: 'Om os',
            link: '/',
        },
        {
            label: 'Min konto',
            link: '/',
        }
    ];

    return (
        <nav>
            <div className="hidden md:block max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-center h-16 xl:absolute xl:top-0 xl:right-0">
                    {/* Desktop Menu */}
                    <div className="hidden md:flex space-x-8 items-center">
                        {menu.map((menuItem, index) => {
                            return (
                                <Link
                                    href={menuItem.link}
                                    className="text-[#2B0D2E]"
                                    key={index}
                                >
                                    {menuItem.label}
                                </Link>
                            );
                        })}
                    </div>

                    <button
                        type="button"
                        className="flex items-center justify-center p-2 h-12 text-[#2B0D2E] ml-[30]"
                    >
                        <Image
                            src={getAssetUrl('img/cart.svg')}
                            overrideSrc={getAssetUrl('img/cart.svg')}
                            alt="Cart Summary"
                            width={38}
                            height={32}
                        />
                    </button>
                </div>
            </div>

            <div>
                <div className="md:hidden flex items-center justify-stretch">
                    {/* Mobile Menu Button */}
                    <button
                        onClick={() => setIsOpen(!isOpen)}
                        type="button"
                        className="flex items-center justify-center p-2 h-12 text-[#2B0D2E] w-1/2"
                    >
                        {isOpen ? (
                            <svg
                                className="h-6 w-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        ) : (
                            <svg
                                className="h-6 w-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 6h16M4 12h16M4 18h16"
                                />
                            </svg>
                        )}
                    </button>

                    <button
                        type="button"
                        className="flex items-center justify-center p-2 h-12 text-[#2B0D2E] w-1/2"
                    >
                        <Image
                            src={getAssetUrl('img/cart.svg')}
                            overrideSrc={getAssetUrl('img/cart.svg')}
                            alt="Cart Summary"
                            width={38}
                            height={32}
                        />
                    </button>
                </div>

                {/* Mobile Menu */}
                {isOpen && (
                    <div className="md:hidden">
                        <div className="px-2 pt-2 pb-3 space-y-1">
                            {menu.map((menuItem, index) => {
                                return (
                                    <Link
                                        href={menuItem.link}
                                        className="block px-3 py-2 rounded-md text-[#2B0D2E]"
                                        key={index}
                                    >
                                        {menuItem.label}
                                    </Link>
                                );
                            })}
                        </div>
                    </div>
                )}
            </div>
        </nav>
    );
};

export default Navbar;
