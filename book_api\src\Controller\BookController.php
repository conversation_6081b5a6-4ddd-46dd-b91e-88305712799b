<?php

namespace App\Controller;

use App\Book\Book;
use App\Book\Character;
use App\Book\Factory as BookFactory;
use App\Entity\BookImage;
use App\Entity\BookOrder;
use App\Entity\BookPreviewCounter;
use App\Entity\ImageWebhook;
use App\Enums\Ethnicity;
use App\HelperFunctions;
use App\Service\BookGenerator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class BookController extends AbstractController
{
    #[Route('/book/generate', name: 'book_generate', methods: ['POST'])]
    public function generate(Request $request, EntityManagerInterface $entityManager): Response {
        $mainCharacterPhoto = $request->getPayload()->get('main_character');

        if ($mainCharacterPhoto) {
            $fileName = basename($mainCharacterPhoto);
            $uploadedFile = new UploadedFile($mainCharacterPhoto, $fileName);
        } else {
            /**
             * @var \Symfony\Component\HttpFoundation\File\UploadedFile
             */
            $uploadedFile = $request->files->get('main_character');
        }

        $bookName = $request->getPayload()->get('book');
        $age = $request->getPayload()->get('age');
        $name = $request->getPayload()->get('name');
        $gender = $request->getPayload()->get('gender');
        $ethnicity = $request->getPayload()->get('ethnicity');
        $hairStyle = $request->getPayload()->get('hair_style');
        $hairLength = $request->getPayload()->get('hair_length');
        $ipAddress = $request->getPayload()->get('ip');

        $idCart = $request->getPayload()->get('id_cart');
        $idScene = $request->getPayload()->get('id_scene');
        $isPreview = (int) $request->getPayload()->get('is_preview');

        if (!$uploadedFile || !$age || !$name || !$gender || !$ipAddress) {
            return new Response(
                'Bad request',
                Response::HTTP_BAD_REQUEST,
                [
                    'content-type' => 'application/json'
                ]
            );
        }

        if (
            $uploadedFile->getMimeType() !== 'image/jpeg' &&
            $uploadedFile->getMimeType() !== 'image/png' &&
            $uploadedFile->getMimeType() !== 'image/webp'
        ) {
            return new Response(
                'Bad request 2',
                Response::HTTP_BAD_REQUEST,
                [
                    'content-type' => 'application/json'
                ]
            );
        }

        $counter = $entityManager->getRepository(BookPreviewCounter::class)->findOneByIp($ipAddress);

        $previewLimit = 14;
        $lastPreview = $counter->getLastPreview();
        $diff = (int) $lastPreview->diff(new \DateTime())->format('%s');

        /**
         * 1 Day
         * 
         * @var int
         */
        $maxDiff = 60 * 60 * 24;

        if (
            $counter->getPreviewCount() >= $previewLimit &&
            $diff < $maxDiff
        ) {
            // Abusive usage of preview
            return new Response(
                'Abusive usage detected',
                Response::HTTP_TOO_MANY_REQUESTS
            );
        }

        $filename = 'mc_' . time() . '_' . $uploadedFile->getClientOriginalName();
        $filepath = HelperFunctions::getUploadsDir() .
            '/' . $filename;

        if (is_string($mainCharacterPhoto)) {
            copy(
                $uploadedFile->getPathname(),
                $filepath
            );
        } else {
            move_uploaded_file(
                $uploadedFile->getPathname(),
                $filepath
            );
        }

        if (!file_exists($filepath)) {
            return new Response(
                'File could not be uploaded',

                Response::HTTP_INTERNAL_SERVER_ERROR,

                [
                    'content-type' => 'application/json'
                ]
            );
        }

        $character = new Character();
        $character->age = $age;
        $character->gender = $gender;
        $character->useImage($filepath);

        /**
         * Override with settings sent from browser
         */
        if ($ethnicity) {
            $character->ethnicity = $ethnicity;
        }

        if ($hairStyle) {
            $character->hairStyle = $hairStyle;
        }

        if ($hairLength) {
            $character->hairLength = $hairLength;
        }

        $book = BookFactory::getBook(
            $bookName,
            $character
        );

        $order = null;
        if ($idCart) {
            $order = $book->addOrder(
                entityManager: $entityManager,
                bookName: $bookName,
                character: $character,
                idCart: $idCart,
                name: $name,
                photo: $filename,
            );
        }

        $book->setCharacter($character);

        $generations = $book->requestGenerateImage(
            $age,
            $gender,
            $isPreview > 0,
            $idScene ? (int) $idScene : null
        );

        $book->saveGenerationIds($entityManager, $idCart, $bookName, $generations, $isPreview > 0);

        $counter->setPreviewCount($counter->getPreviewCount() + 1);
        $counter->setLastPreview(new \DateTime());

        $entityManager->persist($counter);

        if ($order) {
            $order->setIsOrdered(true);
            $order->setIsFinished(true);
            $entityManager->persist($order);
        }

        $entityManager->flush();

        $response = json_encode([
            'generating_images' => true,
        ]);

        return new Response(
            $response,
            Response::HTTP_OK,
            [
                'content-type' => 'application/json'
            ]
        );
    }

    /**
     * Handles ordering of a book
     * 
     * This API Endpoint is usually used after the customer completes their payment
     */
    #[Route('/book/order', name: 'book_order', methods: ['POST'])]
    public function order(Request $request, EntityManagerInterface $entityManager): Response {
        $mainCharacterPhoto = $request->getPayload()->get('main_character');

        if ($mainCharacterPhoto) {
            $fileName = basename($mainCharacterPhoto);
            $uploadedFile = new UploadedFile($mainCharacterPhoto, $fileName);
        } else {
            /**
             * @var \Symfony\Component\HttpFoundation\File\UploadedFile
             */
            $uploadedFile = $request->files->get('main_character');
        }

        $bookName = $request->getPayload()->get('book');
        $age = $request->getPayload()->get('age');
        $name = $request->getPayload()->get('name');
        $gender = $request->getPayload()->get('gender');
        $ethnicity = $request->getPayload()->get('ethnicity');
        $hairStyle = $request->getPayload()->get('hair_style');
        $hairLength = $request->getPayload()->get('hair_length');
        $idCart = $request->getPayload()->get('id_cart');

        if (!$uploadedFile || !$age || !$name || !$gender || !$idCart) {
            return new Response(
                'Bad request',
                Response::HTTP_BAD_REQUEST,
                [
                    'content-type' => 'application/json'
                ]
            );
        }

        if (
            $uploadedFile->getMimeType() !== 'image/jpeg' &&
            $uploadedFile->getMimeType() !== 'image/png' &&
            $uploadedFile->getMimeType() !== 'image/webp'
        ) {
            return new Response(
                'Bad request 2',
                Response::HTTP_BAD_REQUEST,
                [
                    'content-type' => 'application/json'
                ]
            );
        }

        $filename = 'mc_' . time() . '_' . $uploadedFile->getClientOriginalName();
        $filepath = HelperFunctions::getUploadsDir() .
            '/' . $filename;

        if (is_string($mainCharacterPhoto)) {
            copy(
                $uploadedFile->getPathname(),
                $filepath
            );
        } else {
            move_uploaded_file(
                $uploadedFile->getPathname(),
                $filepath
            );
        }

        if (!file_exists($filepath)) {
            return new Response(
                'File could not be uploaded',

                Response::HTTP_INTERNAL_SERVER_ERROR,

                [
                    'content-type' => 'application/json'
                ]
            );
        }

        $character = new Character();
        $character->age = $age;
        $character->gender = $gender;
        $character->useImage($filepath);

        /**
         * Override with settings sent from browser
         */
        if ($ethnicity) {
            $character->ethnicity = $ethnicity;
        }

        if ($hairStyle) {
            $character->hairStyle = $hairStyle;
        }

        if ($hairLength) {
            $character->hairLength = $hairLength;
        }

        $book = BookFactory::getBook(
            $bookName,
            $character
        );

        $book->addOrder(
            entityManager: $entityManager,
            character: $character,
            bookName: $bookName,
            idCart: $idCart,
            name: $name,
            photo: $filename
        );

        $response = json_encode([
            'success' => true,
        ]);

        return new Response(
            $response,
            Response::HTTP_OK,
            [
                'content-type' => 'application/json'
            ]
        );
    }

    /**
     * Handles callback requests from leonardo ai
     * 
     * Does 2 things:
     * - Save image as file
     * - Save image as a image webhook entity
     * 
     * We don't have to worry about cache since there will be no webhook.
     * If a scene is already cached, we don't send a request to leonardo ai.
     * 
     * Sample of request:
     * '{
     *     "data": {
     *         "object": {
     *            "images": [
     *                {
     *                    "id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
     *                    "createdAt": "2023-11-09T00:42:26.733Z",
     *                    "updatedAt": "2023-11-09T00:42:26.733Z",
     *                    "userId": "ef8b8386-94f7-48d1-b10e-e87fd4dee6e6",
     *                    "url": "https://cdn.leonardo.ai/users/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/generations/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/Leonardo_Creative_An_oil_painting_of_a_cat_0.jpg",
     *                    "generationId": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
     *                    "nobgId": null,
     *                    "nsfw": false,
     *                    "likeCount": 0,
     *                    "trendingScore": 0,
     *                    "public": false
     *                }
     *            ],
     *        }
     *    }
     * }'
     */
    #[Route('/book/webhook', name: 'book_webhook', methods: ['POST'])]
    public function webhook(Request $request, EntityManagerInterface $entityManager): Response {
        $authTokenUsed = $request->headers->get('Authorization');

        if ($authTokenUsed !== ('Bearer ' . $_ENV['LEONARDO_WEBHOOK_API_KEY'])) {
            return new Response(
                'Unauthorized',
                Response::HTTP_UNAUTHORIZED,
                [
                    'content-type' => 'application/json'
                ]
            );
        }

        $requestContent = $request->getContent();

        // Store the request content on a file in the same directory
        $logFile = __DIR__ . '/webhook.log';

        $requestData = json_decode($requestContent, true);
        $requestType = $requestData['type'];

        if ($requestType !== 'image_generation.complete') {
            return new Response(
                'Invalid Request',
                Response::HTTP_BAD_REQUEST,
                [
                    'content-type' => 'application/json'
                ]
            );
        }

        $logEntry = date('Y-m-d H:i:s') . " - " . $requestContent . PHP_EOL;
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

        $images = $requestData['data']['object']['images'];

        foreach ($images as $image) {
            $url = $image['url'];
            $generationId = $image['generationId'];

            try {
                $filename = Book::saveImage($url, $generationId);

                $imageWebhook = new ImageWebhook();
                $imageWebhook->setGenerationId($generationId);
                $imageWebhook->setImagePath(HelperFunctions::getBookImageDir() . '/' . $filename);
                $imageWebhook->setSource('Leonardo AI');
                $imageWebhook->setIsProcessing(0);
                $imageWebhook->setIsProcessed(0);

                $entityManager->persist($imageWebhook);
                $entityManager->flush();
            } catch (\Exception $e) {
                $logEntry = date('Y-m-d H:i:s') . " - " . $e->getMessage() . PHP_EOL;
                file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
            }
        }

        return new Response(
            '',
            Response::HTTP_OK,
            [
                'content-type' => 'application/json'
            ]
        );
    }

    /**
     * Endpoint for checking if images have been generated
     */
    #[Route('/book/check', name: 'book_check', methods: ['POST'])]
    public function check(Request $request, EntityManagerInterface $entityManager): Response {
        $idCart = $request->getPayload()->get('id_cart');
        $bookName = $request->getPayload()->get('book');

        if (!$idCart) {
            return new Response(
                'Bad Request',
                Response::HTTP_BAD_REQUEST
            );
        }

        /**
         * @var BookImage
         */
        $images = $entityManager->getRepository(BookImage::class)->findByCartId($idCart);

        $isComplete = true;
        $imageList = [];
        foreach ($images as $image) {
            if (!$image->getImagePath()) {
                $isComplete = false;
                break;
            }

            $imagePath = HelperFunctions::getBookImageDir() . '/' . $image->getImagePath();

            if (!file_exists($imagePath)) {
                $isComplete = false;
                continue;
            }

            if (
                $bookName &&
                $image->getBookName() !== $bookName
            ) {
                continue;
            }

            $imageList[] = 'https://book.magipa.shoptech.media/public/generations/' . $image->getImagePath();
        }

        $responseData = json_encode([
            'images' => $imageList,
            'is_complete' => $isComplete,
        ]);

        return new Response(
            $responseData,
            Response::HTTP_OK,
            [
                'content-type' => 'application/json'
            ]
        );
    }

    /**
     * Endpoint for cron job that periodically generates images and books
     */
    #[Route('/book/cron-generate', name: 'book_cron_generate', methods: ['GET'])]
    public function cron_generate(Request $request, EntityManagerInterface $entityManager): Response {
        ini_set('max_execution_time', '60');

        $orders = $entityManager->getRepository(BookOrder::class)->findNotOrdered();

        if (!$orders) {
            return new Response('');
        }

        $start = time();

        $i = 0;
        $timeSpent = 0;
        $maxTimeSpent = 30; // seconds

        do {
            if (!isset($orders[$i])) {
                break;
            }

            $order = $orders[$i];

            $filename = $order->getCharacterPhoto();
            $filepath = HelperFunctions::getUploadsDir() . '/' . $filename;

            $character = new Character();
            $character->age = $order->getAge();
            $character->gender = $order->getGender();
            $character->useImage($filepath);

            /**
             * Override with settings sent from browser
             */
            if ($order->getEthnicity()) {
                $character->ethnicity = $order->getEthnicity();
            }
    
            if ($order->getHairStyle()) {
                $character->hairStyle = $order->getHairStyle();
            }
    
            if ($order->getHairLength()) {
                $character->hairLength = $order->getHairLength();
            }

            $book = BookFactory::getBook(
                $order->getBookName(),
                $character
            );

            if (!file_exists($filepath)) {
                return new Response('Cannot find main character photo');
            }

            $book->setCharacter($character);

            $generations = $book->requestGenerateImageAsync(
                $order->getAge(),
                $order->getGender(),
                false
            );

            foreach ($generations as $generation) {
                $book->saveGenerationIds(
                    $entityManager,
                    $order->getIdCart(),
                    $order->getBookName(),
                    [$generation],
                    false
                );
            }

            $order->setIsOrdered(true);
            $entityManager->persist($order);
            $entityManager->flush();

            $i++;
            $timeSpent = time() - $start;
        } while ($timeSpent < $maxTimeSpent);

        $entityManager->flush();

        return new Response('');
    }

    /**
     * Handles image webhooks from leonardo ai or other sources with polling
     * Polls for unprocessed webhooks with 1-second delays when none are found
     */
    #[Route('/book/cron-webhook', name: 'book_cron_webhook', methods: ['GET'])]
    public function cron_webhook(Request $request, EntityManagerInterface $entityManager): Response {
        // Set maximum execution time to 60 seconds
        ini_set('max_execution_time', '60');

        $processingLimit = 10;
        $maxExecutionTime = 60; // seconds
        $pollDelay = 1; // seconds
        $startTime = time();
        $processedCount = 0;

        while ((time() - $startTime) < $maxExecutionTime) {
            /**
             * Images currently being processed
             *
             * @var ImageWebhook[]
             */
            $processing = $entityManager->getRepository(ImageWebhook::class)->findProcessing();

            if (count($processing) >= $processingLimit) {
                // Wait before checking again if at capacity
                sleep($pollDelay);
                continue;
            }

            $availableSlot = $processingLimit - count($processing);

            if ($availableSlot <= 0) {
                // Wait before checking again if no available slots
                sleep($pollDelay);
                continue;
            }

            /**
             * Images not processed yet
             *
             * @var ImageWebhook[]
             */
            $unprocessed = $entityManager->getRepository(ImageWebhook::class)->findUnprocessed();

            if (!$unprocessed || empty($unprocessed)) {
                // No unprocessed webhooks found, wait 1 second and check again
                sleep($pollDelay);
                continue;
            }

            $imageWebhook = $unprocessed[0];

            if (!$imageWebhook) {
                // No valid webhook found, wait and continue
                sleep($pollDelay);
                continue;
            }

            if (!file_exists($imageWebhook->getImagePath())) {
                // Mark as processed if image file doesn't exist
                $imageWebhook->setIsProcessed(1);
                $imageWebhook->setIsProcessing(0);
                $entityManager->persist($imageWebhook);
                $entityManager->flush();

                // Continue to next iteration without delay since we processed something
                continue;
            }

            try {
                // Mark as processing
                $imageWebhook->setIsProcessed(0);
                $imageWebhook->setIsProcessing(1);
                $entityManager->persist($imageWebhook);
                $entityManager->flush();

                Book::saveGeneratedImage(
                    $entityManager,
                    $imageWebhook->getGenerationId(),
                    $imageWebhook->getImagePath()
                );

                // Mark as processed
                $imageWebhook->setIsProcessed(1);
                $imageWebhook->setIsProcessing(0);
                $entityManager->persist($imageWebhook);
                $entityManager->flush();

                $processedCount++;

                // Continue to next iteration without delay since we processed something
                continue;

            } catch (\Exception $e) {
                $logFile = __DIR__ . '/webhook.log';
                $logEntry = date('Y-m-d H:i:s') . " - " . $imageWebhook->getGenerationId() . " - " . $e->getMessage() . PHP_EOL;
                file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

                // Mark as not processing on error
                $imageWebhook->setIsProcessing(0);
                $entityManager->persist($imageWebhook);
                $entityManager->flush();

                // Continue to next iteration
                continue;
            }
        }

        $executionTime = time() - $startTime;
        $responseMessage = $processedCount > 0
            ? "Processed {$processedCount} webhooks in {$executionTime} seconds"
            : "No webhooks processed in {$executionTime} seconds";

        return new Response(
            $responseMessage,
            Response::HTTP_OK
        );
    }

    /**
     * Endpoint for cron job that periodically generates images and books
     */
    #[Route('/book/cron-order', name: 'book_cron_order', methods: ['GET'])]
    public function cron_order(Request $request, EntityManagerInterface $entityManager): Response {
        ini_set('max_execution_time', '60');

        $orders = $entityManager->getRepository(BookOrder::class)->findReadyToOrder();

        $start = time();

        $i = 0;
        $timeSpent = 0;
        $maxTimeSpent = 30; // seconds

        do {
            if (!isset($orders[$i])) {
                break;
            }

            $order = $orders[$i];

            /**
             * @var BookImage
             */
            $images = $entityManager->getRepository(BookImage::class)->findByCartId(
                $order->getIdCart()
            );

            $isComplete = true;
            $imageList = [];
            foreach ($images as $image) {
                if (!$image->getImagePath()) {
                    $isComplete = false;
                    break;
                }

                $imagePath = HelperFunctions::getBookImageDir() . '/' . $image->getImagePath();

                if (!file_exists($imagePath)) {
                    $isComplete = false;
                    continue;
                }

                if ($image->getBookName() !== $order->getBookName()) {
                    continue;
                }

                $imageList[] = 'https://book.magipa.shoptech.media/public/generations/' . $image->getImagePath();
            }

            // Do not continue if the images are not yet complete
            if (!$isComplete) {
                $timeSpent = time() - $start;
                continue;
            }

            $timeSpent = time() - $start;
            $book = new BookGenerator(
                order: $order,
                timespent: $timeSpent,
                timeout: $maxTimeSpent
            );

            $book->setBookName($order->getBookName());
            $book->setIdCart($order->getIdCart());
            $book->setAge($order->getAge());
            $book->setGender($order->getGender());
            $book->setName($order->getName());
            $book->setImages($imageList);

            $pdfPath = $book->createBook();

            if (!file_exists($pdfPath)) {
                // Failed to produce a pdf
                $timeSpent = time() - $start;
                continue;
            }

            if (!$pdfPath) {
                $timeSpent = time() - $start;
                continue;
            }

            echo $pdfPath;

            $order->setIsFinished(true);
            $entityManager->persist($order);
            $entityManager->flush();

            $i++;
            $timeSpent = time() - $start;
        } while ($timeSpent < $maxTimeSpent);

        $entityManager->flush();

        return new Response('');
    }
}