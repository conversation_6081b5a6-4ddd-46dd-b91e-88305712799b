# Internally rewrite certain urls without changing the URL in the browser
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Avoid rewriting existing files or directories
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    
    # 1. Handle _next/image requests specifically
    RewriteRule ^_next/image(?:/(.*))?$ /_next/image.php?$1 [L,NC,QSA]
    
    # 2. General rule: Rewrite other _next requests to .next
    RewriteCond %{REQUEST_URI} !^/_next/image
    RewriteRule ^_next/(.*)$ /.next/$1 [L,NC]
</IfModule>
<IfModule mod_rewrite.c>
    RewriteEngine On
    # Ensure the Authorization header is passed to PHP
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    # Redirect to HTTPS (optional but recommended)
    RewriteCond %{HTTPS} !=on
    RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    # If the requested file or directory does not exist, route to index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^ index.php [L]
</IfModule>
# Deny access to sensitive files and directories
<FilesMatch "\.(env|env.local|htaccess|htpasswd|ini|log|yml|yaml|twig|sql|sqlite|lock|git|md)$">
    Require all denied
</FilesMatch>
# Deny access to version control and build files
RedirectMatch 404 /\.git
RedirectMatch 404 /var/
RedirectMatch 404 /vendor/
# Leverage browser caching (optional, improves performance)
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>
# Gzip compression (optional, improves performance)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/xml
</IfModule>