<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

use Intervention\Image\ImageManager;

/**
 * Class MyAccountControllerCore
 *
 * @since 1.0.0
 */
class MyOrdersControllerCore extends FrontController
{
    // @codingStandardsIgnoreStart
    /** @var bool $auth */
    public $auth = true;
    /** @var string $php_self */
    public $php_self = 'my-orders';
    /** @var string $authRedirection */
    public $authRedirection = 'my-orders';
    /** @var bool $ssl */
    public $ssl = true;
    // @codingStandardsIgnoreEnd

    public function __construct () {

        parent::__construct();

        if (!ExtraFeatures::isEnabled('FO_PURCHASE_PAGE')) {
            $myAccountPage = $this->context->link->getPageLink('my-account', true);

            Tools::redirect($myAccountPage);
            exit;
        }

        $this->display_column_left = false;
        $this->display_column_right = false;
    }

    /**
     * Set media
     *
     * @return void
     *
     * @since 1.0.0
     */
    public function setMedia()
    {
        parent::setMedia();
        $this->addCSS(_THEME_CSS_DIR_.'my-orders.css');
        $this->addJS(_THEME_JS_DIR_.'my-orders.js');
    }

    /**
     * Assign template vars related to page content
     *
     * @see   FrontController::initContent()
     *
     * @return void
     *
     * @since 1.0.0
     */
    public function initContent()
    {
        parent::initContent();

        $purchases = $this->getPurchases();

        $this->context->smarty->assign(
            [
                'purchases' => $purchases
            ]
        );

        $this->setTemplate(_PS_THEME_DIR_.'my-orders.tpl');
    }

    public function getPurchases () : array {

        $id_customer = $this->context->customer->id;
        $purchases = [];

        $prefix = _DB_PREFIX_;
        $purchases = Db::getInstance()->ExecuteS("
            SELECT od.*, o.date_add FROM {$prefix}order_detail as od
            LEFT JOIN {$prefix}orders as o ON (o.id_order = od.id_order)
            WHERE o.id_customer = '{$id_customer}'
            ORDER BY od.id_order_detail DESC
        ");

        foreach ($purchases as &$purchase) {
            $purchase['link'] = $this->context->link->getProductLink($purchase['product_id']);
        }

        return $purchases;

    }

    public function postProcess () {

        $prefix = _DB_PREFIX_;

        if (Tools::getValue('action') === 'getCardDetails') {

            echo Tools::jsonEncode($purchase);
            exit;

        }

        if (Tools::getValue('action') === 'createGiftCard') {

            $idPurchase = (int) Tools::getValue('idPurchase');

            $cardDetails = Db::getInstance()->getRow("
                SELECT od.product_name as name, od.product_quantity as quantity, od.total_price_tax_incl as price FROM {$prefix}order_detail as od
                WHERE od.id_order_detail = '{$idPurchase}'
            ");

            $price = number_format($cardDetails['price'], 2);

            // create an image manager instance with favored driver
            $manager = new ImageManager();

            // to finally create image instances
            $image = $manager->make('img/giftcards/Giftcard-birthday.jpg');

            $image->text(
                $price,

                265,

                167,

                function ($font) {
                    $font->size(24);
                }
            );

            echo $image->response('jpg');

            // print_r($cardDetails);

            exit;
        }

    }
}
