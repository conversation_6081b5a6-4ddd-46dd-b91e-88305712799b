<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminMetaControllerCore
 *
 * @since 1.0.0
 */
class AdminTutorialsControllerCore extends AdminController
{
    // @codingStandardsIgnoreStart
    public $table = 'tutorial';
    public $className = 'Tutorial';
    public $lang = true;

    /** @var Meta $object */
    protected $object;
    // @codingStandardsIgnoreEnd

    /**
     * AdminMetaControllerCore constructor.
     *
     * @since 1.0.0
     */
    public function __construct()
    {
        $this->bootstrap = true;
        $this->identifier_name = 'page';
        $this->lang = true;

        $this->addRowAction('edit');
        $this->addRowAction('delete');

        $this->explicitSelect = true;

        $this->allow_export = true;

        $this->deleted = false;
        $this->context = Context::getContext();

        $this->_orderBy = 'id_tutorial';
        $this->_orderWay = 'DESC';
        $this->_use_found_rows = true;

        $this->bulk_actions = [
            'delete' => [
                'text'    => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?'),
                'icon'    => 'icon-trash',
            ],
        ];

        $this->fields_list = [
            'id_tutorial'  => [
                'title' => $this->l('ID'),
                'align' => 'text-center',
                'class' => 'fixed-width-xs',
            ],
            'controller_name' => [
                'title' => $this->l('Controller'),
            ],
            'module'       => [
                'title'          => $this->l('Module')
            ],
        ];

        parent::__construct();
    }

    /**
     * Render form
     *
     * @return string
     *
     * @since 1.0.0
     */
    public function renderForm()
    {
        if (!$this->loadObject(true)) {
            return '';
        }

        if (Validate::isLoadedObject($this->object)) {
            $this->display = 'edit';
        } else {
            $this->display = 'add';
        }

        $this->initToolbar();
        $this->initPageHeaderToolbar();

        $this->fields_form = [
            'legend'  => [
                'title' => $this->l('Tutorials'),
                'icon'  => 'icon-folder-close',
            ],

            'input'   => [
                [
                    'type' => 'text',
                    'label' => $this->l('Controller'),
                    'name' => 'controller_name'
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Module'),
                    'name' => 'module'
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Video'),
                    'name' => 'video',
                    'lang' => true,
                ],
            ],

            'submit'  => [
                'title' => $this->l('Save'),
            ]
        ];

        $this->field_values = [
            'controller' => 'asdasd',
        ];

        return parent::renderForm();
    }

    /**
     * Post process
     *
     * @return bool
     *
     * @since 1.0.0
     */
    public function postProcess()
    {
        if (Tools::isSubmit('submitAddtutorials')) {
            parent::validateRules();

            if (count($this->errors)) {
                return false;
            }

            if (!$id_tutorial = (int) Tools::getValue('id_tutorial')) {
                $tutorial = new Tutorial();
                $tutorial->controller_name = Tools::getValue('controller_name');
                $tutorial->module = Tools::getValue('module');
                $tutorial->video = Tools::getValue('video');

                if (!$tutorial->add()) {
                    $this->errors[] = Tools::displayError('An error occurred while creating an object.').' <b>'.$this->table.' ('.Db::getInstance()->getMsgError().')</b>';
                } else {
                    $this->updateAssoShop($id_tutorial->id);
                }
            } else {
                $tutorial = new Tutorial($id_tutorial);
                $tutorial->controller_name = Tools::getValue('controller_name');
                $tutorial->module = Tools::getValue('module');
                $tutorial->video = Tools::getValue('video');

                if (!$tutorial->update()) {
                    $this->errors[] = Tools::displayError('An error occurred while updating an object.').' <b>'.$this->table.' ('.Db::getInstance()->getMsgError().')</b>';
                } else {
                    $this->updateAssoShop($tutorial->id);
                }
            }
        } else {
            return parent::postProcess();
        }

        return false;
    }
}
