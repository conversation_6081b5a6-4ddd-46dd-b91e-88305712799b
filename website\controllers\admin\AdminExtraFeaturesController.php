<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminMetaControllerCore
 *
 * @since 1.0.0
 */
class AdminExtraFeaturesControllerCore extends AdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->className = 'ExtraFeatures';
        $this->lang = true;


        parent::__construct();

        if(Tools::isSubmit('saveChangesExtraFeatures')){
            $data = $_POST;
            unset($data['saveChangesExtraFeatures']);
            $config = new ExtraFeatures();
            $result = $config::update($data);
        }
    }
    public function setMedia()
    	{
    		parent::setMedia();
        $this->addCSS(_PS_MODULE_DIR_.'extrafeatures/extrafeatures.css', 'all');
        $this->addJS(_PS_MODULE_DIR_.'extrafeatures/extrafeatures.js');

    	}
    public function renderList()
    {
        $form = $this->renderForm();

        // To load form inside your template
        $this->context->smarty->assign('form_tpl', $form);

        return $this->context->smarty->fetch(_PS_MODULE_DIR_.'extrafeatures/extrafeatures.tpl');

        // To return form html only
        // ! Not Needed
        return $form;
    }

    private function isModuleEnabled (string $moduleName) : bool {
        return Module::isEnabled($moduleName) && Module::isInstalled($moduleName);
    }

    public function renderForm() {
        $config = new ExtraFeatures();

        $data = $config->getData();

        $getCategory = new Category();
        $getCategory = Category::getCategories($this->context->language->id, $active=true, $order=false);

        $categories = array();
        foreach($getCategory as $category){
          $token[] = array();
          $token['key'] = $category['id_category'];
          $token['name'] = $category['name'];
          $categories[] = $token;
        }

        $fields_form_1 = array(
            'form' => array(
                'legend' => array('title' => $this->l('Settings for Extra Features'), 'icon' => 'icon-cogs'),

                'input' => array(
                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Search TNT'),
                        'name' => 'searchtnt',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Holiday'),
                        'name' => 'HolidaySale',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('POS'),
                        'name' => 'wkpos',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Add To Cart'),
                        'name' => 'FO_EASY_ADD_TO_CART',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Recognize Existing Account and Checkout Login'),
                        'name' => 'FO_EASY_CHECKOUT_LOGIN',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Product and Category Pages'),
                        'name' => 'combinationsincatalog',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Recommended Products'),
                        'name' => 'accessorycheckbox',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Image Delete Button'),
                        'name' => 'BO_FEATURE_IMG_DEL',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Content Boxes'),
                        'name' => 'linkslider',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('My Purchase Page'),
                        'name' => 'FO_PURCHASE_PAGE',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Gift Cards'),
                        'name' => 'giftcard',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),
                            
                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Inspiration'),
                        'name' => 'FO_INSPIRATION_PAGE',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                       'class' => 'select-category',
                       'type' => 'select',
                       'name' => 'FO_INSPIRATION_PAGE_CATEGORY',
                       'options' => array(
                           'query' => $categories,
                           'id' => 'key',
                           'name' => 'name'
                       )
                   ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Newsletter'),
                        'name' => 'iqitpopup',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Sticky Notes'),
                        'name' => 'advancedordernotes',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'sticky_notes_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'sticky_notes_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),

                    array(
                        'class' => 'fixed-width-xl',
                        'type' => 'switch',
                        'label' => $this->l('Faq'),
                        'name' => 'faqs',
                        'required' => false,
                        'values' => array(
                            array(
                                'id' => 'faqs_on',
                                'value' => 1,
                                'label' => $this->l('Active')
                            ),

                            array(
                                'id' => 'faqs_off',
                                'value' => 0,
                                'label' => $this->l('Inactive')
                            )
                        )
                    ),
                ),

                'submit' => array(
                    'title' => $this->l('Save'),
                    'type' => 'submit'
                ),

                'buttons' => array(
                    array(
                        'href' => AdminController::$currentIndex . '&action=test&token=' . $this->token,
                        'title' => $this->l('Cancel'),
                        'icon' => 'process-icon-cancel'
                    )
                )
            ),
        );

        $this->fields_form = array();

        $lang = new Language((int) Configuration::get('PS_LANG_DEFAULT'));

        $helper = new HelperForm();

        $helper->show_toolbar = false;

        $helper->module = $this;

        $helper->name_controller = $this->name;

        $helper->default_form_language = $lang->id;

        $helper->identifier = $this->identifier;

        $helper->submit_action = 'saveChangesExtraFeatures';

        $helper->currentIndex = AdminController::$currentIndex;

        $helper->token = $this->token;

        $helper->tpl_vars = array(
            'fields_value' => $data,
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id
        );

        return $helper->generateForm(array($fields_form_1));
    }
}
