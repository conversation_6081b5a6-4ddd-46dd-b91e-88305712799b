<?php

namespace App;

use Exception;

class HelperFunctions
{
    public static function getUploadsDir()
    {
        return self::getRootDir() . '/public/uploads';
    }

    public static function getBookImageDir()
    {
        return self::getRootDir() . '/public/generations';
    }

    /**
     * Returns the absolute path to the root directory
     */
    public static function getRootDir()
    {
        return dirname(__DIR__);
    }

    /**
     * Hashes a file input along with other parameters
     * 
     * @param string $filePath
     * @param mixed $otherParams
     * 
     * @return string
     */
    public static function hashFileInput($filePath, ...$otherParams)
    {
        // Check if the image exists
        if (!file_exists($filePath)) {
            throw new Exception("File not found.");
        }

        $fileHash = hash_file('sha256', $filePath);

        $inputData = [
            $fileHash,
            ...$otherParams
        ];

        $cacheKey = hash('sha256', json_encode($inputData));

        return $cacheKey;
    }
}