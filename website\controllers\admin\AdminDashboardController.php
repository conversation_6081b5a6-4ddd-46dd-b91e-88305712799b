<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminDashboardControllerCore
 *
 * @since 1.0.0
 */
class AdminDashboardControllerCore extends AdminController
{
    const ERROR_STATUS = '5,6,7';

    /**
     * AdminDashboardControllerCore constructor.
     *
     * @since 1.0.0
     */
    public function __construct()
    {
        $this->bootstrap = true;
        $this->display = 'view';

        parent::__construct();

        if (Tools::isSubmit('profitability_conf') || Tools::isSubmit('submitOptionsconfiguration')) {
            $this->fields_options = $this->getOptionFields();
        }
    }

    /**
     * @return array
     *
     * @since 1.0.0
     */
    protected function getOptionFields()
    {
        $forms = [];
        $currency = new Currency(Configuration::get('PS_CURRENCY_DEFAULT'));
        $carriers = Carrier::getCarriers($this->context->language->id, true, false, false, null, 'ALL_CARRIERS');
        $modules = Module::getModulesOnDisk(true);

        $forms = [
            'payment'  => ['title' => $this->l('Average bank fees per payment method'), 'id' => 'payment'],
            'carriers' => ['title' => $this->l('Average shipping fees per shipping method'), 'id' => 'carriers'],
            'other'    => ['title' => $this->l('Other settings'), 'id' => 'other'],
        ];
        foreach ($forms as &$form) {
            $form['icon'] = 'tab-preferences';
            $form['fields'] = [];
            $form['submit'] = ['title' => $this->l('Save')];
        }

        foreach ($modules as $module) {
            if (isset($module->tab) && $module->tab == 'payments_gateways' && $module->id) {
                $moduleClass = Module::getInstanceByName($module->name);
                if (!$moduleClass->isEnabledForShopContext()) {
                    continue;
                }

                $forms['payment']['fields']['CONF_'.strtoupper($module->name).'_FIXED'] = [
                    'title'        => $module->displayName,
                    'desc'         => sprintf($this->l('Choose a fixed fee for each order placed in %1$s with %2$s.'), $currency->iso_code, $module->displayName),
                    'validation'   => 'isPrice',
                    'cast'         => 'priceval',
                    'type'         => 'text',
                    'defaultValue' => '0',
                    'suffix'       => $currency->iso_code,
                ];
                $forms['payment']['fields']['CONF_'.strtoupper($module->name).'_VAR'] = [
                    'title'        => $module->displayName,
                    'desc'         => sprintf($this->l('Choose a variable fee for each order placed in %1$s with %2$s. It will be applied on the total paid with taxes.'), $currency->iso_code, $module->displayName),
                    'validation'   => 'isPercentage',
                    'cast'         => 'floatval',
                    'type'         => 'text',
                    'defaultValue' => '0',
                    'suffix'       => '%',
                ];

                if (Currency::isMultiCurrencyActivated()) {
                    $forms['payment']['fields']['CONF_'.strtoupper($module->name).'_FIXED_FOREIGN'] = [
                        'title'        => $module->displayName,
                        'desc'         => sprintf($this->l('Choose a fixed fee for each order placed with a foreign currency with %s.'), $module->displayName),
                        'validation'   => 'isPrice',
                        'cast'         => 'priceval',
                        'type'         => 'text',
                        'defaultValue' => '0',
                        'suffix'       => $currency->iso_code,
                    ];
                    $forms['payment']['fields']['CONF_'.strtoupper($module->name).'_VAR_FOREIGN'] = [
                        'title'        => $module->displayName,
                        'desc'         => sprintf($this->l('Choose a variable fee for each order placed with a foreign currency with %s. It will be applied on the total paid with taxes.'), $module->displayName),
                        'validation'   => 'isPercentage',
                        'cast'         => 'floatval',
                        'type'         => 'text',
                        'defaultValue' => '0',
                        'suffix'       => '%',
                    ];
                }
            }
        }

        foreach ($carriers as $carrier) {
            $forms['carriers']['fields']['CONF_'.strtoupper($carrier['id_reference']).'_SHIP'] = [
                'title'        => $carrier['name'],
                'desc'         => sprintf($this->l('For the carrier named %s, indicate the domestic delivery costs  in percentage of the price charged to customers.'), $carrier['name']),
                'validation'   => 'isPercentage',
                'cast'         => 'floatval',
                'type'         => 'text',
                'defaultValue' => '0',
                'suffix'       => '%',
            ];
            $forms['carriers']['fields']['CONF_'.strtoupper($carrier['id_reference']).'_SHIP_OVERSEAS'] = [
                'title'        => $carrier['name'],
                'desc'         => sprintf($this->l('For the carrier named %s, indicate the overseas delivery costs in percentage of the price charged to customers.'), $carrier['name']),
                'validation'   => 'isPercentage',
                'cast'         => 'floatval',
                'type'         => 'text',
                'defaultValue' => '0',
                'suffix'       => '%',
            ];
        }

        $forms['carriers']['description'] = $this->l('Method: Indicate the percentage of your carrier margin. For example, if you charge $10 of shipping fees to your customer for each shipment, but you really pay $4 to this carrier, then you should indicate "40" in the percentage field.');

        $forms['other']['fields']['CONF_AVERAGE_PRODUCT_MARGIN'] = [
            'title'        => $this->l('Average gross margin percentage'),
            'desc'         => $this->l('You should calculate this percentage as follows: ((total sales revenue) - (cost of goods sold)) / (total sales revenue) * 100. This value is only used to calculate the Dashboard approximate gross margin, if you do not specify the wholesale price for each product.'),
            'validation'   => 'isPercentage',
            'cast'         => 'intval',
            'type'         => 'text',
            'defaultValue' => '0',
            'suffix'       => '%',
        ];

        $forms['other']['fields']['CONF_ORDER_FIXED'] = [
            'title'        => $this->l('Other fees per order'),
            'desc'         => $this->l('You should calculate this value by making the sum of all of your additional costs per order.'),
            'validation'   => 'isPrice',
            'cast'         => 'priceval',
            'type'         => 'text',
            'defaultValue' => '0',
            'suffix'       => $currency->iso_code,
        ];

        Media::addJsDef(
            [
                'dashboard_ajax_url' => $this->context->link->getAdminLink('AdminDashboard'),
                'read_more'          => '',
            ]
        );

        return $forms;
    }

    /**
     * @since 1.0.0
     */
    public function setMedia()
    {
        parent::setMedia();

        $this->addJS(
            [
                $this->theme_path.'/plugins/charts-c3/d3.v5.min.js',
                $this->theme_path.'/plugins/charts-c3/c3-chart.js',
                $this->theme_path.'/js/index.js'
            ]
        );

        // $this->addCSS($this->admin_webpath.'/themes/'.$this->bo_theme.'/css/vendor/nv.d3.css');
    }

    /**
     * @since 1.0.0
     */
    public function initPageHeaderToolbar()
    {
        $this->page_header_toolbar_title = $this->l('Dashboard');

        $this->page_header_toolbar_btn['switch_demo'] = [
            'desc' => $this->l('Demo mode', null, null, false),
            'help' => $this->l('This mode displays sample data so you can try your dashboard without real numbers.', null, null, false),
        ];

        $this->page_header_toolbar_btn['update_system'] = [
            'desc' => $this->l('Update System', null, null, false),
            'href' => $this->context->link->getAdminLink('AdminUpdates'),
        ];

        parent::initPageHeaderToolbar();

        // Remove the last element on this controller to match the title with the rule of the others
        array_pop($this->meta_title);
    }

    public function setDates(){

        $DREPDATE = Configuration::get('PIWIK_DREPDATE');

        if(!$this->context->employee->stats_date_from){
            if ($DREPDATE !== FALSE && (strpos($DREPDATE, '|') !== FALSE)) {
                list($period, $date) = explode('|', $DREPDATE);

                $dates = explode(',', $date);

                $this->context->employee->stats_date_from = $dates[0];
                $this->context->employee->stats_date_to = $dates[1];
            } else {
                $period = "day";
                $date = "today";

                $this->context->employee->stats_date_from = date('Y-m-d');
                $this->context->employee->stats_date_to = date('Y-m-d');
            }

            $this->context->employee->update();
        }
    }

    public function getGraph (array $params) : array {

        $prefix = _DB_PREFIX_;

        $graphs = [
            'sales' => [
                'header' => [],
                'value' => []
            ],

            'orders' => [
                'header' => [],
                'value' => []
            ],

            'net_profits' => [
                'header' => [],
                'value' => []
            ],
        ];

        $from = strtotime($this->context->employee->stats_date_from);
        $to = strtotime($this->context->employee->stats_date_to);

        $current = $from;

        $matomoSiteId = (int) Analytics::getSitesIdFromSiteUrl($this->context->shop->domain_ssl);

        $data = [
            'idSite' => $matomoSiteId,
            'method' => 'API.getBulkRequest',
            'period' => 'range',
            'urls' => [],
        ];

        $interval = ($to - $from) / 12;

        while ($current < $to) {
            $current_date = date('m/d/y', $current);

            $from_date = date('Y-m-d', $current);
            $to_date = date('Y-m-d', ($current + $interval) - (60 * 60 * 24)) . ' 23:59:59';

            $graphs['sales']['header'][] = $current_date;
            $graphs['orders']['header'][] = $current_date;
            $graphs['net_profits']['header'][] = $current_date;

            $graphs['sales']['value'][] = 0;
            $graphs['orders']['value'][] = 0;
            $graphs['net_profits']['value'][] = 0;

            $subData = [
                'method' => 'Goals.get',
                'idGoal' => 'ecommerceOrder',
                'idSite' => $matomoSiteId,
                'date' => $from_date . ',' . $to_date,
                'period' => 'range'
            ];

            $data['urls'][] = http_build_query($subData);

            // Go to next day
            $current += $interval;

        }

        $ecommerceData = Analytics::reportingApi($data);

        foreach ($ecommerceData as $i => $data) {
            if ($data->revenue) {
                $graphs['sales']['value'][$i] = $data->revenue;
            }

            if ($data->nb_conversions) {
                $graphs['orders']['value'][$i] = $data->nb_conversions;
            }

            if ($data->revenue) {
                $graphs['net_profits']['value'][$i] = $data->revenue - ($data->revenue_tax + $data->revenue_shipping);
            }
        }

        return $graphs;
    }

    protected function setDashboard () {

        $time_from = "{$this->context->employee->stats_date_from}";
        $time_to = "{$this->context->employee->stats_date_to}";

        $matomoSiteId = Analytics::getSitesIdFromSiteUrl($this->context->shop->domain_ssl);
        $ecommerceData = Analytics::getEcommerceData($matomoSiteId, 'range', $time_from . ',' . $time_to);
        $itemsData = Analytics::getItems($matomoSiteId, 'range', $time_from . ',' . $time_to);
        $bounces = Analytics::getBounceCount($matomoSiteId, 'range', $time_from . ',' . $time_to);
        $visits = Analytics::getVisits($matomoSiteId, 'range', $time_from . ',' . $time_to);

        $bounce_rate = 0;

        if($bounces[0]->value > 0 && $visits[0]->value > 0) {
            $bounce_rate = ($bounces[0]->value / $visits[0]->value) * 100;
        }

        $products = [];
        foreach ($itemsData as $item) {
            if ($item->quantity > 0) {
                $products[] = [
                    'id_product' => 0,
                    'name' => $item->label,
                    'totalQuantitySold' => $item->quantity,
                    'totalPriceSold' => $item->revenue,
                ];
            }
        }

        $this->tpl_view_vars = [
            'hasNewUpdates'      => $this->hasNewUpdates(),
            'date_from'          => $this->context->employee->stats_date_from,
            'date_to'            => $this->context->employee->stats_date_to,
            'preselect_date_range' => $this->context->employee->preselect_date_range,

            'CurrencySign' => $this->context->currency->sign,
            'visits' => $visits[0]->value,
            'in_cart' => 0,
            'total_sales' => number_format((float) $ecommerceData[0]->revenue, 2),
            'total_profit' => number_format((float) ($ecommerceData[0]->revenue - ($ecommerceData[0]->revenue_tax + $ecommerceData[0]->revenue_shipping)), 2),
            'total_orders' => $ecommerceData[0]->nb_conversions,
            'conversion' => number_format((float) $ecommerceData[0]->conversion_rate, 2),
            'products_sold' => $products,

            'bounce_rate' => number_format($bounce_rate, 2)
        ];

        $params = [
            'date_from'          => $this->context->employee->stats_date_from,
            'date_to'            => $this->context->employee->stats_date_to,
            'compare_from'       => $this->context->employee->stats_compare_from,
            'compare_to'         => $this->context->employee->stats_compare_to,
            'dashboard_use_push' => (int) Tools::getValue('dashboard_use_push'),
            'extra'              => (int) Tools::getValue('extra'),
        ];

        if ((strtotime($params['date_to']) - strtotime($params['date_from'])) < (7 * 25 * 60 * 60)) {
            $params['date_from'] = date('Y-m-d', strtotime($params['date_to']) - (7 * 25 * 60 * 60) );
        }

        $graphs = $this->getGraph($params);

        Media::addJsDef([
            'StatsOrdersProfit' => [],
            'graphs' => $graphs,
            'TotalOrdersTxt' => $this->l('Orders'),
            'TotalSalesTxt' => $this->l('Total Sales'),
            'TotalProfitsTxt' => $this->l('Total Profit'),
            'PS_CSS_THEME_CACHE' => (bool) \Configuration::get('PS_CSS_THEME_CACHE'),
            'PS_JS_THEME_CACHE' => (bool) \Configuration::get('PS_JS_THEME_CACHE'),
            'PS_SMARTY_CACHE' => (bool) \Configuration::get(\Configuration::SMARTY_CACHE),
        ]);
        
    }

    /**
     * @return string
     *
     * @since 1.0.0
     */
    public function renderView()
    {
        $this->setDates();

        if (Tools::isSubmit('profitability_conf')) {
            return parent::renderOptions();
        }

        $this->setDashboard();

        return parent::renderView();
    }

    /**
     * @return bool|null|string
     *
     * @since 1.0.0
     */
    protected function getWarningDomainName()
    {
        $warning = false;
        if (Shop::isFeatureActive()) {
            return null;
        }

        $shop = $this->context->shop;
        if ($_SERVER['HTTP_HOST'] != $shop->domain && $_SERVER['HTTP_HOST'] != $shop->domain_ssl && Tools::getValue('ajax') == false && !defined('_PS_HOST_MODE_')) {
            $warning = $this->l('You are currently connected under the following domain name:').' <span style="color: #CC0000;">'.$_SERVER['HTTP_HOST'].'</span><br />';
            if (Configuration::get('PS_MULTISHOP_FEATURE_ACTIVE')) {
                $warning .= sprintf($this->l('This is different from the shop domain name set in the Multistore settings: "%s".'), $shop->domain).'
				'.preg_replace('@{link}(.*){/link}@', '<a href="index.php?controller=AdminShopUrl&id_shop_url='.(int) $shop->id.'&updateshop_url&token='.Tools::getAdminTokenLite('AdminShopUrl').'">$1</a>', $this->l('If this is your main domain, please {link}change it now{/link}.'));
            } else {
                $warning .= $this->l('This is different from the domain name set in the "SEO & URLs" tab.').'
				'.preg_replace('@{link}(.*){/link}@', '<a href="index.php?controller=AdminMeta&token='.Tools::getAdminTokenLite('AdminMeta').'#meta_fieldset_shop_url">$1</a>', $this->l('If this is your main domain, please {link}change it now{/link}.'));
            }
        }

        return $warning;
    }

    /**
     * @since 1.0.0
     */
    public function postProcess()
    {
        if (Tools::getValue('action') === 'hideUpdateIndicator') {
            Configuration::updateValue('HAS_NEW_UPDATES', 0);
        }

        if (Tools::isSubmit('submitDateRealTime')) {
            if ($useRealtime = (int) Tools::getValue('submitDateRealTime')) {
                $this->context->employee->stats_date_from = date('Y-m-d');
                $this->context->employee->stats_date_to = date('Y-m-d');
                $this->context->employee->stats_compare_option = HelperCalendar::DEFAULT_COMPARE_OPTION;
                $this->context->employee->stats_compare_from = null;
                $this->context->employee->stats_compare_to = null;
                $this->context->employee->update();
            }

            Configuration::updateValue('PS_DASHBOARD_USE_PUSH', $useRealtime);
        }

        if(Tools::isSubmit('submitSetRange')){
            $range = Tools::getValue('submitSetRange');

            switch ($range) {
                case 'submitDateDay':

                    $timeFrom = strtotime('Today');
                    $timeTo = strtotime('Today');

                    $this->context->employee->preselect_date_range = 'day';

                break;

                case 'submitDateMonth':

                    $timeFrom = strtotime('First Day of This Month');
                    $timeTo = strtotime('Last Day of This Month');

                    $this->context->employee->preselect_date_range = 'month';

                break;

                case 'submitDateYear':

                    $year = (int) date('Y');

                    $timeFrom = strtotime('First Day of January ' . $year);
                    $timeTo = strtotime('Last Day of December ' . $year);

                    $this->context->employee->preselect_date_range = 'year';

                break;

                case 'submitDateDayPrev':

                    $timeFrom = strtotime('Yesterday');
                    $timeTo = strtotime('Yesterday');

                    $this->context->employee->preselect_date_range = 'prev-day';

                break;

                case 'submitDateMonthPrev':

                    $timeFrom = strtotime('First Day of Last Month');
                    $timeTo = strtotime('Last Day of Last Month');

                    $this->context->employee->preselect_date_range = 'prev-month';

                break;

                case 'submitDateYearPrev':

                    $year = (int) date('Y');
                    $year -= 1;

                    $timeFrom = strtotime('First Day of January ' . $year);
                    $timeTo = strtotime('Last Day of December ' . $year);

                    $this->context->employee->preselect_date_range = 'prev-year';

                break;

                default:
                    $timeFrom = strtotime(Tools::getValue('date_from'));
                    $timeTo = strtotime(Tools::getValue('date_to'));
                    # code...
                break;
            }

            $this->context->employee->stats_date_from = date('Y-m-d', $timeFrom);
            $this->context->employee->stats_date_to = date('Y-m-d', $timeTo);

            $this->context->employee->update();
        }

        if (Tools::isSubmit('submitDateRange')) {
            if (!Validate::isDate(Tools::getValue('date_from'))
                || !Validate::isDate(Tools::getValue('date_to'))
            ) {
                $this->errors[] = Tools::displayError('The selected date range is not valid.');
            }

            if (Tools::getValue('datepicker_compare')) {
                if (!Validate::isDate(Tools::getValue('compare_date_from'))
                    || !Validate::isDate(Tools::getValue('compare_date_to'))
                ) {
                    $this->errors[] = Tools::displayError('The selected date range is not valid.');
                }
            }

            if (!count($this->errors)) {
                $this->context->employee->stats_date_from = Tools::getValue('date_from');
                $this->context->employee->stats_date_to = Tools::getValue('date_to');
                $this->context->employee->preselect_date_range = Tools::getValue('preselectDateRange');

                if (Tools::getValue('datepicker_compare')) {
                    $this->context->employee->stats_compare_from = Tools::getValue('compare_date_from');
                    $this->context->employee->stats_compare_to = Tools::getValue('compare_date_to');
                    $this->context->employee->stats_compare_option = Tools::getValue('compare_date_option');
                } else {
                    $this->context->employee->stats_compare_from = null;
                    $this->context->employee->stats_compare_to = null;
                    $this->context->employee->stats_compare_option = HelperCalendar::DEFAULT_COMPARE_OPTION;
                }

                $this->context->employee->update();
            }
        }

        parent::postProcess();
    }

    /**
     * @since 1.0.0
     */
    public function ajaxProcessRefreshDashboard()
    {
        $idModule = null;
        if ($module = Tools::getValue('module')) {
            $moduleObj = Module::getInstanceByName($module);
            if (Validate::isLoadedObject($moduleObj)) {
                $idModule = $moduleObj->id;
            }
        }

        $params = [
            'date_from'          => $this->context->employee->stats_date_from,
            'date_to'            => $this->context->employee->stats_date_to,
            'compare_from'       => $this->context->employee->stats_compare_from,
            'compare_to'         => $this->context->employee->stats_compare_to,
            'dashboard_use_push' => (int) Tools::getValue('dashboard_use_push'),
            'extra'              => (int) Tools::getValue('extra'),
        ];

        $this->ajaxDie(json_encode(Hook::exec('dashboardData', $params, $idModule, true, true, (int) Tools::getValue('dashboard_use_push'))));
    }

    /**
     * @since 1.0.0
     */
    public function ajaxProcessSetSimulationMode()
    {
        Configuration::updateValue('PS_DASHBOARD_SIMULATION', (int) Tools::getValue('PS_DASHBOARD_SIMULATION'));
        $this->ajaxDie('k'.Configuration::get('PS_DASHBOARD_SIMULATION').'k');
    }

    /**
     * @since 1.0.0
     */
    public function ajaxProcessGetBlogRss()
    {
        $return = array('has_errors' => false, 'rss' => array());
        if (!$this->isFresh('/config/xml/blog-'.$this->context->language->iso_code.'.xml', 86400)) {
            if (!$this->refresh('/config/xml/blog-'.$this->context->language->iso_code.'.xml', 'https://shoptech.media/feed/')) {
                $return['has_errors'] = true;
            }
        }
        if (!$return['has_errors']) {
            $rss = @simplexml_load_file(_PS_ROOT_DIR_.'/config/xml/blog-'.$this->context->language->iso_code.'.xml');
            if (!$rss) {
                $return['has_errors'] = true;
            }
            $articlesLimit = 2;
            if ($rss) {
                foreach ($rss->channel->item as $item) {
                    if ($articlesLimit > 0 && Validate::isCleanHtml((string) $item->title) && Validate::isCleanHtml((string) $item->description)
                        && isset($item->link) && isset($item->title)
                    ) {
                        if (in_array($this->context->mode, array(Context::MODE_HOST, Context::MODE_HOST_CONTRIB))) {
                            $utmContent = 'cloud';
                        } else {
                            $utmContent = 'download';
                        }
                        $shopDefaultCountryId = (int) Configuration::get('PS_COUNTRY_DEFAULT');
                        $shopDefaultIsoCountry = (string) mb_strtoupper(Country::getIsoById($shopDefaultCountryId));
                        $analyticsParams = array(
                            'utm_source'   => 'back-office',
                            'utm_medium'   => 'rss',
                            'utm_campaign' => 'back-office-'.$shopDefaultIsoCountry,
                            'utm_content'  => $utmContent,
                        );
                        $urlQuery = parse_url($item->link, PHP_URL_QUERY);
                        parse_str($urlQuery, $linkQueryParams);
                        if ($linkQueryParams) {
                            $fullUrlParams = array_merge($linkQueryParams, $analyticsParams);
                            $baseUrl = explode('?', (string) $item->link);
                            $baseUrl = (string) $baseUrl[0];
                            $articleLink = $baseUrl.'?'.http_build_query($fullUrlParams);
                        } else {
                            $articleLink = (string) $item->link.'?'.http_build_query($analyticsParams);
                        }
                        $return['rss'][] = array(
                            'date'       => Tools::displayDate(date('Y-m-d', strtotime((string) $item->pubDate))),
                            'title'      => (string) Tools::htmlentitiesUTF8($item->title),
                            'short_desc' => Tools::truncateString(strip_tags((string) $item->description), 150),
                            'link'       => (string) $articleLink,
                        );
                    } else {
                        break;
                    }
                    $articlesLimit--;
                }
            }
        }
        $this->ajaxDie(json_encode($return));
    }

    /**
     * @since 1.0.0
     */
    public function ajaxProcessSaveDashConfig()
    {
        $return = ['has_errors' => false, 'errors' => []];
        $module = Tools::getValue('module');
        $hook = Tools::getValue('hook');
        $configs = Tools::getValue('configs');

        $params = [
            'date_from' => $this->context->employee->stats_date_from,
            'date_to'   => $this->context->employee->stats_date_to,
        ];

        if (Validate::isModuleName($module) && $moduleObj = Module::getInstanceByName($module)) {
            if (Validate::isLoadedObject($moduleObj) && method_exists($moduleObj, 'validateDashConfig')) {
                $return['errors'] = $moduleObj->validateDashConfig($configs);
            }
            if (!count($return['errors'])) {
                if (Validate::isLoadedObject($moduleObj) && method_exists($moduleObj, 'saveDashConfig')) {
                    $return['has_errors'] = $moduleObj->saveDashConfig($configs);
                } elseif (is_array($configs) && count($configs)) {
                    foreach ($configs as $name => $value) {
                        if (Validate::isConfigName($name)) {
                            Configuration::updateValue($name, $value);
                        }
                    }
                }
            } else {
                $return['has_errors'] = true;
            }
        }

        if (Validate::isHookName($hook) && method_exists($moduleObj, $hook)) {
            $return['widget_html'] = $moduleObj->$hook($params);
        }

        $this->ajaxDie(json_encode($return));
    }

    protected function calculateConversionRate (int $totalOrders, int $totalInteractions) : float {
        if ($totalInteractions > 0 && $totalOrders > 0) {
            $conversionRate = ($totalOrders / $totalInteractions) * 100;
        } else {
            $conversionRate = 0.00;
        }

        return $conversionRate;
    }

    protected function calculateBounceRate (int $totalVisits) : float {
        $analytic_prefix = _ANALYTICS_PREFIX_;
        $bounces = (int) Db::getInstance()->getValue("SELECT COUNT(idvisit) FROM {$analytic_prefix}log_visit WHERE visit_total_interactions <= 1 AND visit_first_action_time >= '{$this->context->employee->stats_date_from} 00:00:00' AND visit_last_action_time <= '{$this->context->employee->stats_date_to} 23:59:59'");

        if ($bounces <=0 || $totalVisits <= 0)
            return 0.00;

        return $bounces / $totalVisits;

    }

    protected function exportCsv (string $fileName, array $rows) {

        header( 'Content-Type: text/csv' );
        header( 'Content-Disposition: attachment;filename='.$fileName . '.csv');

        foreach ($rows as $row) {
            $out = fopen('php://output', 'w');

            fputcsv($out, $row);

            fclose($out);
        }

    }

    protected function hasNewUpdates () {
        return (int) Configuration::get('HAS_NEW_UPDATES');
    }
}