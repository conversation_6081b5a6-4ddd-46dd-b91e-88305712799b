<?php

namespace App\Entity;

use App\Repository\BookOrderRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BookOrderRepository::class)]
class BookOrder
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $book_name = null;

    #[ORM\Column]
    private ?int $id_cart = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column]
    private ?int $age = null;

    #[ORM\Column(length: 255)]
    private ?string $gender = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $ethnicity = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $hair_style = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $hair_length = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $character_photo = null;

    #[ORM\Column]
    private ?bool $is_ordered = false;

    #[ORM\Column]
    private ?bool $is_finished = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getBookName(): ?string
    {
        return $this->book_name;
    }

    public function setBookName(string $book_name): static
    {
        $this->book_name = $book_name;

        return $this;
    }

    public function getIdCart(): ?int
    {
        return $this->id_cart;
    }

    public function setIdCart(int $id_cart): static
    {
        $this->id_cart = $id_cart;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getAge(): ?int
    {
        return $this->age;
    }

    public function setAge(int $age): static
    {
        $this->age = $age;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(string $gender): static
    {
        $this->gender = $gender;

        return $this;
    }

    public function getEthnicity(): ?string
    {
        return $this->ethnicity ?? '';
    }

    public function setEthnicity(string $ethnicity): static
    {
        $this->ethnicity = $ethnicity;

        return $this;
    }

    public function getHairStyle(): ?string
    {
        return $this->hair_style ?? '';
    }

    public function setHairStyle(string $hairStyle): static
    {
        $this->hair_style = $hairStyle;

        return $this;
    }

    public function getHairLength(): ?string
    {
        return $this->hair_length ?? '';
    }

    public function setHairLength(string $hairLength): static
    {
        $this->hair_length = $hairLength;

        return $this;
    }

    public function getCharacterPhoto(): ?string
    {
        return $this->character_photo;
    }

    public function setCharacterPhoto(string $character_photo): static
    {
        $this->character_photo = $character_photo;

        return $this;
    }

    public function getIsOrdered(): ?bool
    {
        return $this->is_ordered;
    }

    public function setIsOrdered(bool $is_ordered): static
    {
        $this->is_ordered = $is_ordered;

        return $this;
    }

    public function getIsFinished(): ?bool
    {
        return $this->is_finished;
    }

    public function setIsFinished(bool $is_finished): static
    {
        $this->is_finished = $is_finished;

        return $this;
    }
}
