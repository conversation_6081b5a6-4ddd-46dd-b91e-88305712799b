<?php

namespace App\Cache;

use App\HelperFunctions;

class Cache
{
    public static function getCacheFolder()
    {
        return HelperFunctions::getRootDir() . '/cache';
    }

    /**
     * Checks if a cache exists given a specific key
     * 
     * @param string $key
     * 
     * @return bool
     */
    public static function exists($key)
    {
        return file_exists(self::getCacheFolder() . '/' . $key);
    }

    /**
     * Returns the cached object given a specific key
     * 
     * @param string $key
     * 
     * @return object
     */
    public static function getObject($key)
    {
        $object = json_decode(file_get_contents(self::getCacheFolder() . '/' . $key));

        return $object;
    }

    /**
     * Caches an object to the given key
     * 
     * @param string $key
     * @param object $object
     * 
     * @return void
     */
    public static function cacheObject($key, $object)
    {
        file_put_contents(self::getCacheFolder() . '/' . $key, json_encode($object));
    }
}
