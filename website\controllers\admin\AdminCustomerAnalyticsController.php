<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminDashboardControllerCore
 *
 * @since 1.0.0
 */
class AdminCustomerAnalyticsControllerCore extends AdminDashboardController
{
    /**
     * AdminDashboardControllerCore constructor.
     *
     * @since 1.0.0
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @since 1.0.0
     */
    public function setMedia()
    {
        parent::setMedia();
    }

    /**
     * @since 1.0.0
     */
    public function initPageHeaderToolbar()
    {
        parent::initPageHeaderToolbar();

        unset($this->page_header_toolbar_btn['switch_demo']);
        unset($this->page_header_toolbar_btn['update_system']);
    }

    protected function setDashboard () {

        $customers = $this->getBestCustomers();

        $this->tpl_view_vars = [
            'date_from'          => $this->context->employee->stats_date_from,
            'date_to'            => $this->context->employee->stats_date_to,
            'preselect_date_range' => $this->context->employee->preselect_date_range,

            'customers' => $customers,
        ];

    }

    private function getBestCustomers () : array {

        $prefix = _DB_PREFIX_;

        $customers = (array) Db::getInstance()->ExecuteS("
            SELECT o.id_customer, c.email, CONCAT(c.firstname, ' ', c.lastname) name, COUNT(o.id_order) as orders, SUM(o.total_paid_tax_incl) as totalAmountBought FROM {$prefix}orders as o
            INNER JOIN {$prefix}customer as c ON (c.id_customer = o.id_customer)
            INNER JOIN {$prefix}order_state as os ON (os.id_order_state = o.current_state)
            WHERE o.date_add >= '{$this->context->employee->stats_date_from}' AND o.date_add <= '{$this->context->employee->stats_date_to}' AND (os.delivery = 1 OR os.shipped = 1 OR os.paid = 1)
            GROUP BY o.id_customer
            ORDER BY totalAmountBought DESC
        ");

        return $customers;

    }

    /**
     * @return string
     *
     * @since 1.0.0
     */
    public function renderView()
    {
        return parent::renderView();
    }

    /**
     * @since 1.0.0
     */
    public function postProcess()
    {
        $prefix = _DB_PREFIX_;

        if (Tools::isSubmit('export')) {
            $rows = [];
            $rows[] = [
                $this->l('ID'),
                $this->l('Email'),
                $this->l('Firstname'),
                $this->l('Lastname'),
                $this->l('Orders'),
                $this->l('Spent'),
            ];

            $customers = (array) Db::getInstance()->ExecuteS("
                SELECT o.id_customer, c.email, c.firstname, c.lastname, COUNT(o.id_order) as orders, SUM(o.total_paid_tax_incl) as totalAmountBought FROM {$prefix}orders as o
                INNER JOIN {$prefix}customer as c ON (c.id_customer = o.id_customer)
                WHERE o.date_add >= '{$this->context->employee->stats_date_from}' AND o.date_add <= '{$this->context->employee->stats_date_to}'
                GROUP BY o.id_customer
                ORDER BY totalAmountBought DESC
            ");

            foreach ($customers as $customer) {
                $rows[] = [
                    $customer['id_customer'],
                    $customer['email'],
                    $customer['firstname'],
                    $customer['lastname'],
                    $customer['orders'],
                    $customer['totalAmountBought'],
                ];
            }

            $this->exportCsv('best_customers', $rows);
            exit;
        }

        parent::postProcess();
    }
}