<?php

namespace App\Api;

interface APIClientInterface
{
    /**
     * Sends a GET request
     */
    public function get(string $url, array $params = []): mixed;

    /**
     * Sends a POST request
     */
    public function post(string $url, array $params = []): mixed;

    /**
     * Sends a PUT request
     */
    public function put(string $url, array $params = []): mixed;

    /**
     * Sends a DELETE request
     */
    public function delete(string $url, array $params = []): mixed;

}