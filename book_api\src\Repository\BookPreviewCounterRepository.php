<?php

namespace App\Repository;

use App\Entity\BookPreviewCounter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<BookPreviewCounter>
 */
class BookPreviewCounterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BookPreviewCounter::class);
    }

    //    /**
    //     * @return BookPreviewCounter[] Returns an array of BookPreviewCounter objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('b')
    //            ->andWhere('b.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('b.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    /**
     * Returns the Preview Counter given the IP Address
     * 
     * @param string $ip IP Address
     */
    public function findOneByIp($ip): ?BookPreviewCounter
    {
        $counter = $this->createQueryBuilder('b')
            ->andWhere('b.ip = :ip')
            ->setParameter('ip', $ip)
            ->getQuery()
            ->getOneOrNullResult()
        ;

        if (!$counter) {
            $counter = new BookPreviewCounter();
            $counter->setIp($ip);
            $counter->setLastPreview(new \DateTime());
            $counter->setPreviewCount(0);
        }

        return $counter;
    }
}
