<?php

class AnalyticsCore {

    protected static $token = 'f4a9732c148040da1f157dd763e38739';

    public function __construct($query, $label = '', $format = 'JSON')
    {
        $this->image_dir = _PS_COL_IMG_DIR_;

        $this->context = Context::getContext();

        $PIWIK_SITEID = (int) Configuration::get('PIWIK_SITEID');
        $DREPDATE = Configuration::get('PIWIK_DREPDATE');

        if ($DREPDATE !== FALSE && (strpos($DREPDATE, '|') !== FALSE)) {
            list($period, $date) = explode('|', $DREPDATE);
        } else {
            $period = "day";
            $date = "today";
        }

        $query['module'] = 'API';
        $query['token_auth'] = Configuration::get('PIWIK_TOKEN_AUTH');
        $query['idSite'] = $PIWIK_SITEID;

        if(!isset($query['date'])) {
            $query['date'] = $date;
            $query['period'] = $period;
        }else{
            $query['period'] = 'range';
        }

        $query['language'] = $this->context->language->iso_code;

        $this->raw_query = $query;

        $query = http_build_query($query);

        if($label){ 
            $label = str_replace(' ', '%2520', $label);
            $label = str_replace(':', '%253A', $label);
            $label = str_replace(',', '%252C', $label);
            $label = str_replace('@', '%40', $label);
        }

        $this->query = $query . '&label=' . $label . '&format=' . $format;

        $this->response = false;

        $this->getData();
    }

    private function getData(){
    	$url = 'https://' .  $this->context->shop->domain_ssl . '/analytics/index.php?' . $this->query;

        $call = curl_init();

        curl_setopt($call, CURLOPT_URL, $url);
        curl_setopt($call, CURLOPT_RETURNTRANSFER, true);

        $output = curl_exec($call);

        curl_close($call);

    	$this->response = Tools::jsonDecode($output);

        /**
         * Check if response is an array first before counting
         */
        if(is_array($this->response) && count($this->response) <= 1)
    	   $this->response = @$this->response[0];
    }

    public static function formatData($data){
        return http_build_query($data);
    }

    public static function getBulkData($set){

        $PIWIK_SITEID = (int) Configuration::get('PIWIK_SITEID');
        $DREPDATE = Configuration::get('PIWIK_DREPDATE');

        $urls = [];

        foreach ($set as $data) {

            $data['idSite'] = $PIWIK_SITEID;
            $data['period'] = 'range';

            $urls[] = static::formatData($data);

        }

        $data = [

            'method' => 'API.getBulkRequest',

            'module' => 'API',

            'urls' => $urls,

        ];

        return $data;

    }

    // This function is deprecated
    public static function setPageVisit ($cookie) {
        return true;
        if (Analytics::isTrafficFromCache()) {
            return false;
        }

        $context = Context::getContext();

        // Required variables
        $matomoSiteId = Analytics::getSitesIdFromSiteUrl($context->shop->domain_ssl); // Site ID
        $matomoUrl = 'http://' . $context->shop->domain_ssl . '/analytics'; // Your matomo URL
        $matomoToken = self::$token; // Your authentication token

        // Optional variable
        $matomoPageTitle = $context->smarty->tpl_vars['meta_title']->value; // The title of the page

        // Matomo object
        $matomoTracker = new MatomoTracker((int)$matomoSiteId, $matomoUrl);
        $matomoTracker->setRequestTimeout(30);

        // Set authentication token
        $matomoTracker->setTokenAuth($matomoToken);

        // Set ip address
        $matomoTracker->setIp(Tools::getRemoteAddr());

        // Track page view
        $a = $matomoTracker->doTrackPageView($matomoPageTitle);

        // Connection::setPageConnection($cookie);
    }

    private static function isTrafficFromCache () {
        if (Tools::isSubmit('rebuildCache')) {
            return true;
        }

        return false;
    }

    public static function reportingApi ($data) {

        $context = Context::getContext();

        $data['module'] = 'API';
        $data['token_auth'] = self::$token;
        $data['format'] = 'json';

        $requestUrl = 'https://' . $context->shop->domain_ssl . '/analytics/?' . http_build_query($data);

        exec("curl -sS '{$requestUrl}'", $output);

        $ret = Tools::jsonDecode($output[0]);

        if (!is_array($ret)) {
            $ret = [$ret];
        }

        return $ret;
    }

    public static function getSitesIdFromSiteUrl ($domain) {
        $domain = urlencode($domain);

        $data = [
            'method' => 'SitesManager.getSitesIdFromSiteUrl',
            'url' => $domain,
        ];

        $output = Analytics::reportingApi($data);

        if (is_array($output)) {
            return $output[0]->idsite;
        } else {
            return 1;
        }
    }

    public static function addSite ($name, $domain, $timeZone, $currency) {
        return true;

    }

    public static function updateSite ($idSite, $name, $domain, $timeZone, $currency) {
        return true;
    }

    public static function getGoals ($idSite, $period, $date) {
        $data = [
            'idSite' => $idSite,
            'method' => 'Goals.get',
            'period' => $period,
            'date' => $date,
        ];

        return Analytics::reportingApi($data);
    }

    public static function getEcommerceData ($idSite, $period, $date) {
        $data = [
            'idSite' => $idSite,
            'method' => 'Goals.get',
            'idGoal' => 'ecommerceOrder',
            'period' => $period,
            'date' => $date,
        ];

        return Analytics::reportingApi($data);
    }

    public static function getItems ($idSite, $period, $date, $sort = 'quantity') {
        $data = [
            'idSite' => $idSite,
            'method' => 'Goals.getItemsName',
            'period' => $period,
            'date' => $date,
            'filter_sort_column' => $sort,
            'filter_sort_order' => 'desc',
            'filter_limit' => 10,
        ];

        return Analytics::reportingApi($data);
    }

    public static function getBounceCount ($idSite, $period, $date) {
        $data = [
            'idSite' => $idSite,
            'method' => 'VisitsSummary.getBounceCount',
            'period' => $period,
            'date' => $date
        ];

        return Analytics::reportingApi($data);
    }

    public static function getVisits ($idSite, $period, $date) {
        $data = [
            'idSite' => $idSite,
            'method' => 'VisitsSummary.getVisits',
            'period' => $period,
            'date' => $date
        ];

        return Analytics::reportingApi($data);
    }

}