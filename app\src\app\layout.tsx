import type { Metadata } from "next";
import localFont from 'next/font/local'
import "./globals.css";
import { getAssetUrl } from "@/tools";
import Image from "next/image";
import Navbar from "@/components/Navbar";

const playfair = localFont({
    src: '../fonts/Playfair_Display/PlayfairDisplay-VariableFont_wght.ttf',
    display: 'swap',
    variable: '--font-playfair',
})

const poly = localFont({
    src: '../fonts/Poly/Poly-Regular.ttf',
    display: 'swap',
    variable: '--font-poly',
})

export const metadata: Metadata = {
    title: "Create Next App",
    description: "Generated by create next app",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={`${playfair.variable} ${poly.variable} font-playfair antialiased min-h-screen`}
            >
                {/* <Image
                    src="/frontpage-preview.jpg"
                    width={1920}
                    height={4730}
                    alt="preview"
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        opacity: 0.5,
                        zIndex: -1
                    }}
                /> */}

                <header className="main-header pt-3">
                    <div className="xl:max-w-[1400px] xl:mx-auto xl:relative">
                        <Image
                            src={getAssetUrl('img/logo.svg')}
                            overrideSrc={getAssetUrl('img/logo.svg')}
                            alt=""
                            width={250}
                            height={235}
                            className="mx-auto relative left-3"
                        />

                        <Navbar />

                        <p className="yellow-gradient-banner-text stroke-black stroke-1 text-right mx-2 xl:mt-[128px]">
                            Make your child the hero of their own story
                        </p>
                    </div>

                    <div className="relative text-center pb-5 sm:pb-16 xl:pb-[87px] xl:mt-[124px]">
                        <svg
                            className="absolute bottom-0 left-0 right-0 mx-auto"
                            viewBox="0 0 1400 274"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            style={{
                                maxWidth: 1400
                            }}
                        >
                            <path d="M0 273.482C0 273.482 160.999 0.481689 699.999 0.481689C1239 0.481689 1400 273.482 1400 273.482H0Z" fill="url(#paint0_linear_16_4)" />
                            <defs>
                                <linearGradient id="paint0_linear_16_4" x1="700" y1="0.481689" x2="700" y2="273.482" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#DED3B8" />
                                    <stop offset="1" stop-color="#FFFDF9" />
                                </linearGradient>
                            </defs>
                        </svg>

                        <Image
                            className="w-auto lg:w-[945] relative m-auto"
                            src={getAssetUrl('img/banner.png')}
                            overrideSrc={getAssetUrl('img/banner.png')}
                            alt="Make your child the hero of their own story"
                            width={945}
                            height={342}
                        />
                    </div>
                </header>

                {children}

                <footer></footer>
            </body>
        </html>
    );
}
