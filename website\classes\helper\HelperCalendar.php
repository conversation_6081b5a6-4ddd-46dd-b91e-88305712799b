<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class HelperCalendarCore
 *
 * @since 1.0.0
 */
class HelperCalendarCore extends Helper
{
    const DEFAULT_DATE_FORMAT    = 'Y-mm-dd';
    const DEFAULT_COMPARE_OPTION = 1;

    // @codingStandardsIgnoreStart
    private $_actions;
    private $_compare_actions;
    private $_compare_date_from;
    private $_compare_date_to;
    private $_compare_date_option;
    private $_date_format;
    private $_date_from;
    private $_date_to;
    private $_rtl;
    // @codingStandardsIgnoreEnd

    /**
     * HelperCalendarCore constructor.
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function __construct()
    {
        $this->base_folder = 'helpers/calendar/';
        $this->base_tpl = 'calendar.tpl';
        parent::__construct();
    }

    /**
     * @param Traversable[] $value
     *
     * @return $this
     * @throws PrestaShopException
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setActions($value)
    {
        if (!is_array($value) && !$value instanceof Traversable) {
            throw new PrestaShopException('Actions value must be an traversable array');
        }

        $this->_actions = $value;

        return $this;
    }

    /**
     * @return array
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getActions()
    {
        if (!isset($this->_actions)) {
            $this->_actions = [];
        }

        return $this->_actions;
    }

    /**
     * @param Traversable[] $value
     *
     * @return $this
     * @throws PrestaShopException
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setCompareActions($value)
    {
        if (!is_array($value) && !$value instanceof Traversable) {
            throw new PrestaShopException('Actions value must be an traversable array');
        }

        $this->_compare_actions = $value;

        return $this;
    }

    /**
     * @return array
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getCompareActions()
    {
        if (!isset($this->_compare_actions)) {
            $this->_compare_actions = [];
        }

        return $this->_compare_actions;
    }

    /**
     * @param string $value
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setCompareDateFrom($value)
    {
        $this->_compare_date_from = $value;

        return $this;
    }

    /**
     * @return string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getCompareDateFrom()
    {
        return $this->_compare_date_from;
    }

    /**
     * @param string $value
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setCompareDateTo($value)
    {
        $this->_compare_date_to = $value;

        return $this;
    }

    /**
     * @return string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getCompareDateTo()
    {
        return $this->_compare_date_to;
    }

    /**
     * @param int $value
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setCompareOption($value)
    {
        $this->_compare_date_option = (int) $value;

        return $this;
    }

    /**
     * @return int
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getCompareOption()
    {
        if (!isset($this->_compare_date_option)) {
            $this->_compare_date_option = static::DEFAULT_COMPARE_OPTION;
        }

        return $this->_compare_date_option;
    }

    /**
     * @param string $value
     *
     * @return $this
     * @throws PrestaShopException
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setDateFormat($value)
    {
        if (!is_string($value)) {
            throw new PrestaShopException('Date format must be a string');
        }

        $this->_date_format = $value;

        return $this;
    }

    /**
     * @return string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getDateFormat()
    {
        if (!isset($this->_date_format)) {
            $this->_date_format = static::DEFAULT_DATE_FORMAT;
        }

        return $this->_date_format;
    }

    /**
     * @param string $value
     *
     * @return $this
     * @throws PrestaShopException
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setDateFrom($value)
    {
        if (!isset($value) || $value == '') {
            $value = date('Y-m-d', strtotime('-31 days'));
        }

        if (!is_string($value)) {
            throw new PrestaShopException('Date must be a string');
        }

        $this->_date_from = $value;

        return $this;
    }

    /**
     * @return false|string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getDateFrom()
    {
        if (!isset($this->_date_from)) {
            $this->_date_from = date('Y-m-d', strtotime('-31 days'));
        }

        return $this->_date_from;
    }

    /**
     * @param string $value
     *
     * @return $this
     * @throws PrestaShopException
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setDateTo($value)
    {
        if (!isset($value) || $value == '') {
            $value = date('Y-m-d');
        }

        if (!is_string($value)) {
            throw new PrestaShopException('Date must be a string');
        }

        $this->_date_to = $value;

        return $this;
    }

    /**
     * @return false|string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getDateTo()
    {
        if (!isset($this->_date_to)) {
            $this->_date_to = date('Y-m-d');
        }

        return $this->_date_to;
    }

    /**
     * @param bool $value
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setRTL($value)
    {
        $this->_rtl = (bool) $value;

        return $this;
    }

    /**
     * @param string $action
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function addAction($action)
    {
        if (!isset($this->_actions)) {
            $this->_actions = [];
        }

        $this->_actions[] = $action;

        return $this;
    }

    /**
     * @param string $action
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function addCompareAction($action)
    {
        if (!isset($this->_compare_actions)) {
            $this->_compare_actions = [];
        }

        $this->_compare_actions[] = $action;

        return $this;
    }

    /**
     * @return string
     *
     * @throws Exception
     * @throws SmartyException
     * @since   1.0.0
     * @version 1.0.0 Initial version
     */
    public function generate()
    {
        $context = Context::getContext();
        $adminWebpath = $context->controller->admin_webpath;
        $boTheme = ((Validate::isLoadedObject($context->employee)
            && $context->employee->bo_theme) ? $context->employee->bo_theme : 'default');

        if (!file_exists(_PS_BO_ALL_THEMES_DIR_.$boTheme.DIRECTORY_SEPARATOR.'template')) {
            $boTheme = 'default';
        }

        if ($context->controller->ajax) {
            $html = '<script type="text/javascript" src="'.$adminWebpath.'/themes/'.$boTheme.'/js/date-range-picker.js"></script>';
            $html .= '<script type="text/javascript" src="'.$adminWebpath.'/themes/'.$boTheme.'/js/calendar.js"></script>';
        } else {
            $html = '';
            $context->controller->addJs($adminWebpath.'/themes/'.$boTheme.'/js/date-range-picker.js');
            $context->controller->addJs($adminWebpath.'/themes/'.$boTheme.'/js/calendar.js');
        }

        $this->tpl = $this->createTemplate($this->base_tpl);
        $this->tpl->assign(
            [
                'date_format'       => $this->getDateFormat(),
                'date_from'         => $this->getDateFrom(),
                'date_to'           => $this->getDateTo(),
                'compare_date_from' => $this->getCompareDateFrom(),
                'compare_date_to'   => $this->getCompareDateTo(),
                'actions'           => $this->getActions(),
                'compare_actions'   => $this->getCompareActions(),
                'compare_option'    => $this->getCompareOption(),
                'is_rtl'            => $this->isRTL(),
            ]
        );

        $html .= parent::generate();

        return $html;
    }

    /**
     * @return bool
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function isRTL()
    {
        if (!isset($this->_rtl)) {
            $this->_rtl = Context::getContext()->language->is_rtl;
        }

        return $this->_rtl;
    }
}
