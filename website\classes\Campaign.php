<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class GroupCore
 *
 * @since 1.0.0
 */
class CampaignCore extends ObjectModel
{
    // @codingStandardsIgnoreStart
    protected static $cache_reduction = [];

    public $name;
    public $description;
    public $allow_restart;
    public $published;
    public $published_at;
    public $unpublished_at;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table'     => 'campaign',
        'primary'   => 'id_campaign',
        'multilang' => false,
        'fields'    => [
            'name'                 => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true],
            'description'          => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml', 'required' => false],

            'allow_restart'          => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],
            'published'          => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => false],

            'published_at'          => ['type' => self::TYPE_DATE, 'required' => false],
            'unpublished_at'          => ['type' => self::TYPE_DATE, 'required' => false]
        ],
    ];

    protected $webserviceParameters = [];

    /**
     * CampaignCore constructor.
     *
     * @param int|null $id
     * @param int|null $idLang
     * @param int|null $idShop
     *
     * @since   1.0.0
     * @version 1.0.0 Initial version
     */

    public function __construct($id = null, $idLang = null, $idShop = null) {
        parent::__construct($id, $idLang, $idShop);
    }
}
