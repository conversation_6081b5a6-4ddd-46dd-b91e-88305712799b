<?php

namespace App\Enums;

enum Ethnicity: string
{
    case ALL = 'All';
    case AFRICAN_AMERICAN = 'African American';
    case WHITE = 'White';
    case ASIAN = 'Asian';
    case ARAB = 'Arab';
    case LATINO = 'Latino';
    case LATINA = 'Latina';
    case MIXED = 'Mixed';
    case JEWISH = 'Jewish';

    /**
     * Returns the equivalent Ethnicity given the name
     * 
     * @param string $ethnicity
     * 
     * @return Ethnicity
     */
    public static function getEthnicity(string $ethnicity): Ethnicity
    {
        foreach (Ethnicity::cases() as $case) {
            if ($case->name === $ethnicity) {
                return $case;
            }
        }

        return Ethnicity::WHITE;
    }
}
