<?php

namespace App\Repository;

use App\Entity\BookImage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<BookImage>
 */
class BookImageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BookImage::class);
    }

    /**
    * @return BookImage[] Returns an array of BookImage objects
    */
    public function findByGenerationId(string $generationId): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.generation_id = :generation_id')
            ->setParameter('generation_id', $generationId)
            ->orderBy('b.id', 'DESC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
    * @return BookImage[] Returns an array of BookImage objects
    */
    public function findByCartId(int $idCart): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.id_cart = :id_cart')
            ->setParameter('id_cart', $idCart)
            ->orderBy('b.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Returns an array of BookImage objects that are being processed given the
     * generation id of the image we are looking for
     * 
     * @param string $generationId
     * 
     * @return BookImage[]
     */
    public function findProcessingByGenerationId(string $generationId): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.generation_id = :generation_id')
            ->andWhere('b.is_processing = :is_processing')
            ->setParameter('generation_id', $generationId)
            ->setParameter('is_processing', '1')
            ->orderBy('b.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }
}
