<?php

namespace App\Enums;

enum AgeGroup: string
{
    case ALL = 'all';
    case THREE_TO_FIVE = '3-5';
    case SIX_TO_EIGHT = '6-8';
    case NINE_TO_TWELVE = '9-12';

    /**
     * Returns the equivalent AgeGroup given the value
     * 
     * @param string $ageGroup
     * 
     * @return AgeGroup
     */
    public static function getAgeGroup(string $ageGroup): AgeGroup
    {
        foreach (AgeGroup::cases() as $case) {
            if ($case->name === $ageGroup) {
                return $case;
            }
        }

        return AgeGroup::ALL;
    }

    /**
     * Returns the equivalent AgeGroup given the value
     *
     * @param string $ageGroup
     *
     * @return AgeGroup
     */
    public static function getAgeGroupFromAge(int $age): AgeGroup
    {
        if ($age >= 3 && $age <= 5) {
            return AgeGroup::THREE_TO_FIVE;
        } else if ($age >= 6 && $age <= 8) {
            return AgeGroup::SIX_TO_EIGHT;
        } else if ($age >= 9 && $age <= 12) {
            return AgeGroup::NINE_TO_TWELVE;
        } else {
            return AgeGroup::ALL;
        }
    }
}
