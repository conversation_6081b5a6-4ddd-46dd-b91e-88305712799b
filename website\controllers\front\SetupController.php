<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class SitemapControllerCore
 *
 * @since 1.0.0
 */
class SetupControllerCore extends FrontController
{
    // @codingStandardsIgnoreStart
    /** @var string $php_self */
    public $php_self = 'setup';
    // @codingStandardsIgnoreEnd

    private $fields;

    public function __construct() {
        $_GET['content_only'] = 1;

        parent::__construct();

        $this->display_column_left = false;
        $this->$display_column_right = false;
    }

    public function init () {
        parent::init();

        $setup = new Install();
        $setup->init();

        $opening_hours = Tools::jsonDecode(Configuration::get('OpeningHours'));

        if (!$opening_hours)
            $opening_hours = [];

        $countries = Country::getCountries($this->context->cookie->id_lang);

        $countries = array_map(function ($a) {

            return $a['name'];

        }, $countries);

        $this->fields = [

            'logo' => [
                'type' => 'file',
                'label' => 'Shop Logo',
                'name' => 'logo'
            ],

            'is_noindex' => [
                'type' => 'switch',
                'label' => 'No index?',
                'name' => 'noindex'
            ],

            'PS_SHOP_NAME' => [
                'type' => 'text',
                'label' => 'Shop Name',
                'name' => 'storeName'
            ],

            'PS_SHOP_EMAIL' => [
                'type' => 'text',
                'label' => 'Shop Email',
                'name' => 'storeEmail'
            ],

            'PS_SHOP_ADDR1' => [
                'type' => 'text',
                'label' => 'Shop Address',
                'name' => 'contactInformation[PS_SHOP_ADDR1]'
            ],

            'PS_SHOP_CODE' => [
                'type' => 'text',
                'label' => 'Shop Postal Code',
                'name' => 'contactInformation[PS_SHOP_CODE]'
            ],

            'PS_SHOP_CITY' => [
                'type' => 'text',
                'label' => 'Shop City',
                'name' => 'contactInformation[PS_SHOP_CITY]'
            ],

            'PS_COUNTRY_DEFAULT' => [
                'type' => 'dropdown',
                'label' => 'Shop Country',
                'name' => 'contactInformation[PS_SHOP_COUNTRY_ID]',
                'options' => $countries
            ],

            'PS_SHOP_PHONE1' => [
                'type' => 'text',
                'label' => 'Shop Phone',
                'name' => 'contactInformation[PS_SHOP_PHONE1]'
            ],

            'OpeningHours_0' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Monday',

                'children' => [
                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][0]',
                        'value' => $opening_hours->opening[0]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][0]',
                        'value' => $opening_hours->closing[0]
                    ],
                ]
            ],

            'OpeningHours_1' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Tuesday',

                'children' => [

                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][1]',
                        'value' => $opening_hours->opening[1]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][1]',
                        'value' => $opening_hours->closing[1]
                    ],
                ]
            ],

            'OpeningHours_2' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Wednesday',

                'children' => [

                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][2]',
                        'value' => $opening_hours->opening[2]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][2]',
                        'value' => $opening_hours->closing[2]
                    ],
                ]
            ],

            'OpeningHours_3' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Thursday',

                'children' => [

                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][3]',
                        'value' => $opening_hours->opening[3]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][3]',
                        'value' => $opening_hours->closing[3]
                    ],
                ]
            ],

            'OpeningHours_4' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Friday',

                'children' => [

                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][4]',
                        'value' => $opening_hours->opening[4]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][4]',
                        'value' => $opening_hours->closing[4]
                    ],
                ]
            ],

            'OpeningHours_5' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Saturday',

                'children' => [

                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][5]',
                        'value' => $opening_hours->opening[5]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][5]',
                        'value' => $opening_hours->closing[5]
                    ],
                ]
            ],

            'OpeningHours_6' => [
                'type' => 'multiple',
                'label' => 'Open Hours: Sunday',

                'children' => [

                    [
                        'type' => 'text',
                        'name' => 'openingHours[opening][6]',
                        'value' => $opening_hours->opening[6]
                    ],

                    [
                        'type' => 'text',
                        'name' => 'openingHours[closing][6]',
                        'value' => $opening_hours->closing[6]
                    ],
                ]
            ],

            'PS_ROUTE_product_rule' => [
                'type' => 'text',
                'label' => 'Product Friendly URLs',
                'name' => 'friendlyUrls[product_rule]',
                'lang' => true
            ],

            'PS_ROUTE_category_rule' => [
                'type' => 'text',
                'label' => 'Category Friendly URLs',
                'name' => 'friendlyUrls[category_rule]',
                'lang' => true
            ],

            'PS_ROUTE_supplier_rule' => [
                'type' => 'text',
                'label' => 'Supplier Friendly URLs',
                'name' => 'friendlyUrls[supplier_rule]',
                'lang' => true
            ],

            'PS_ROUTE_manufacturer_rule' => [
                'type' => 'text',
                'label' => 'Manufacturer Friendly URLs',
                'name' => 'friendlyUrls[manufacturer_rule]',
                'lang' => true
            ],

            'PS_ROUTE_cms_rule' => [
                'type' => 'text',
                'label' => 'CMS Friendly URLs',
                'name' => 'friendlyUrls[cms_rule]',
                'lang' => true
            ],

            'PS_ROUTE_cms_category_rule' => [
                'type' => 'text',
                'label' => 'CMS Category Friendly URLs',
                'name' => 'friendlyUrls[cms_category_rule]',
                'lang' => true
            ],

        ];
    }

    /**
     * Assign template vars related to page content
     *
     * @see   FrontController::initContent()
     *
     * @return void
     *
     * @since 1.0.0
     */
    public function initContent()
    {
        parent::initContent();

        $this->context->smarty->assign([

            'fields' => $this->fields,
            'id_lang' => $this->context->language->id

        ]);

        $this->setTemplate(_PS_THEME_DIR_ . 'setup.tpl');
    }
}
