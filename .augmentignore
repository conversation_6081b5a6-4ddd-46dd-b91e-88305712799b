# Environment files
.env
.env.*
*.env
.environment
.envrc

# Config directories and files
config/
*/config/
**/config/
config.inc.php
config.php
settings.php
settings.local.php
local_settings.php

# Database configuration
db_slave_server.inc.php
database.yml
database.yaml
database.json

# Symfony specific config (keep framework config but ignore sensitive ones)
book_api/.env
book_api/.env.*
book_api/config/secrets/
book_api/var/
book_api/vendor/

# PrestaShop specific config
website/config/
website/cache/
website/log/
website/logs/
website/tmp/
website/temp/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/
.pnp.*

# Composer
vendor/
composer.phar

# Cache directories
cache/
*/cache/
**/cache/
var/cache/
var/log/
var/sessions/
var/tmp/
tmp/
temp/

# Log files
*.log
logs/
*/logs/
**/logs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Build artifacts
build/
dist/
out/
target/

# Backup files
*.bak
*.backup
*.old
*.orig

# Sensitive files
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
secrets/
*/secrets/
**/secrets/

# Documentation that might contain sensitive info
INSTALL*
CHANGELOG*
HISTORY*

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Archive files (might contain sensitive data)
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Database dumps
*.sql
*.dump
*.db
*.sqlite
*.sqlite3

# Media uploads (might contain user data)
uploads/
*/uploads/
**/uploads/
media/
*/media/
**/media/

# Generated PDFs and images (might contain user content)
*.pdf
*.jpg
*.jpeg
*.png
*.gif
*.webp
*.svg
books/
*/books/
**/books/
