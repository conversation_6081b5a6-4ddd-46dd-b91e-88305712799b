<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */
/**
 * Class AdminNewsletterControllerCore
 *
 * @since 1.0.0
 */
class AdminNewsletterControllerCore extends AdminController
{

    public $name = 'newsletter';
    public $displayName = 'Current Contacts Registered in the Newsletter System';

    public $filters = [];

    /**
     * AdminNewsletterControllerCore constructor.
     *
     * @since 1.0.0
     */
    public function __construct(){

        $this->bootstrap = true;
        $this->table = 'campaign';
        $this->className = 'Campaign';
        $this->list_id = 'campaign';

        $this->addRowAction('edit');
        $this->addRowAction('view');
        $this->addRowAction('delete');

        $this->db = Db::getInstance();
        $this->prefix = _DB_PREFIX_;

        $cookie = new Cookie('psAdmin');
        $this->id_agent = $cookie->id_employee;

        $this->display = 'list';

        parent::__construct();

        $this->fields_list = [
            'id_campaign' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'havingFilter' => true,
            ],

            'name' => [
                'title' => $this->l('Name'),
                'havingFilter' => true,
            ],
        ];
    }

    public function setCSS(){

        parent::setCSS();

        $this->addCSS($this->theme_path . '/css/campaigns.css', 'all');

    }

    public function setJS(){
        
        parent::setJS();

        $this->addJS($this->theme_path . '/js/campaigns.js');
        $this->addJS('https://cdn.jsdelivr.net/npm/@svgdotjs/svg.js@3.0/dist/svg.min.js');

    }

    public function renderView() {

        if(Tools::isSubmit('addData')) {

            $table = Tools::getValue('addData');

            $return = [];

            switch ($table) {

                case 'campaign_flow':

                    $id_campaign = Tools::getValue('id_campaign', 0);

                    $type = Tools::getValue('type', '');

                    $data = Tools::getValue('data', '');

                    $this->db->insert(

                        $table,

                        [
                            'id_campaign' => $id_campaign,
                            'type' => $type,
                            'data' => $data
                        ]

                    );

                    $return = [
                        'id_object' => $this->db->Insert_ID()
                    ];

                break;

                default:

                    $label = Tools::getValue('label', '');
                    $value = 0;

                    $this->db->insert(

                        $table,

                        [
                            'label' => $label
                        ]

                    );

                    $value = $this->db->Insert_ID();

                    $return = [

                        'label' => $label,
                        'value' => $value

                    ];

                break;

            }

            echo Tools::jsonEncode($return);

            exit;
        }

        if (Tools::isSubmit('editData')) {

            $table = Tools::getValue('editData');

            $id_object = (int) Tools::getValue('id_object', 0);

            $return = [];

            switch ($table) {

                case 'campaign_flow':

                    $id_campaign = Tools::getValue('id_campaign', 0);

                    $type = Tools::getValue('type', '');

                    $data = Tools::getValue('data', '');

                    $this->db->update(

                        $table,

                        [
                            'type' => $type,
                            'data' => $data
                        ],

                        'id_object=' . $id_object

                    );

                    $return = [
                        'id_object' => $id_object
                    ];

                break;

                default:

                    $label = Tools::getValue('label', '');
                    $value = 0;

                    $this->db->update(

                        $table,

                        [
                            'label' => $label
                        ],

                        'id_object=' . $id_object

                    );

                    $return = [

                        'label' => $label,
                        'value' => $id_object

                    ];

                break;

            }

            echo Tools::jsonEncode($return);

            exit;

        }

        if (Tools::isSubmit('getData')) {

            $data = [];

            switch(Tools::getValue('getData')){

                case 'NewsletterContent':
 
                    foreach ($this->db->ExecuteS("SELECT id_newsletter_content as id, name FROM {$this->prefix}newsletter_content") as $newsletterContent) {

                        $data[$newsletterContent['id']] = $newsletterContent['name'];
            
                    }

                break;

                case 'ContactSources':

                    $type = Tools::getValue('type');

                    if($type == 'segments') {
                        $sources = $this->db->ExecuteS("
                            SELECT id_contact_segment, name FROM {$this->prefix}contact_segment
                        ");

                        foreach($sources as $source) {

                            $data[ $source['id_contact_segment'] ] = $source['name'];

                        }
                    }

                break;

            }

            echo Tools::jsonEncode([

                'list' => $data,
                'selected' => []

            ]);

            exit;

        }

        if (Tools::isSubmit('deleteData')) {

            $table = Tools::getValue('deleteData');

            $id_object = (int) Tools::getValue('id_object', 0);

            $this->db->delete(

                $table,

                'id_object=' . $id_object

            );

            exit;

        }

        $content_list = $this->getContentList();
        $campaign_flow = $this->getCampaignFlow();

        $tab = 'AdminNewsletterContent';

        Media::AddJsDef([

            'NewsletterContentToken' => Tools::getAdminToken($tab.(int) Tab::getIdFromClassName($tab).(int) $this->context->employee->id),
            'content_list' => $content_list,
            'campaign_flow' => $campaign_flow

        ]);

        return $this->createTemplate('controllers/newsletter/campaigns.tpl')->fetch();

    }

    public function getCampaignFlow () {
        $id_campaign = (int) Tools::getValue('id_campaign');

        $campaign_flow = $this->db->ExecuteS("SELECT id_object, `type`, `data` FROM {$this->prefix}campaign_flow WHERE id_campaign = '{$id_campaign}'");

        foreach ( $campaign_flow as &$item )
            $item['data'] = Tools::jsonDecode($item['data']);

        return $campaign_flow;
    }

    public function getContentList () {
        $content_list = [];

        foreach ($this->db->ExecuteS("SELECT id_newsletter_content as id, name FROM {$this->prefix}newsletter_content") as $newsletterContent) {

            $content_list[$newsletterContent['id']] = $newsletterContent['name'];

        }

        return $content_list;
    }

    public function renderForm() {

        $this->fields_form = [
            'submit' => [
                'title' => $this->l('Save'),
            ],

            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'required' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'required' => false,
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Allow contacts to restart the campaign '),
                    'name' => 'allow_restart',
                    'required' => false,
                    'is_bool'  => true,
                    'values'   => [
                        [
                            'id'    => 'allow_restart_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id'    => 'allow_restart_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Published'),
                    'name' => 'published',
                    'required' => false,
                    'is_bool'  => true,
                    'values'   => [
                        [
                            'id'    => 'published_on',
                            'value' => 1,
                            'label' => $this->l('Enabled'),
                        ],
                        [
                            'id'    => 'published_off',
                            'value' => 0,
                            'label' => $this->l('Disabled'),
                        ],
                    ]
                ],
                [
                    'type' => 'date',
                    'label' => $this->l('Publish at'),
                    'name' => 'published_at',
                    'required' => false,
                ],
                [
                    'type' => 'date',
                    'label' => $this->l('Unpublish at'),
                    'name' => 'unpublished_at',
                    'required' => false,
                ],
            ]
        ];

        return parent::renderForm();

    }
}
