import { getAssetUrl } from "@/tools";
import Image from "next/image";
import Link from "next/link";

const steps = [
    {
        image: getAssetUrl('img/step-bookshelf.svg'),
        title: 'Choose your book',
        description: 'Explore our extensive selection of stories on our bookshelf. Browse through the categories and select the story you desire.'
    },
    {
        image: getAssetUrl('img/step-upload.svg'),
        title: 'Introduce us to your child',
        description: 'Upload a photo of your child, specify their age and gender, and let the magic transform your child into the main character of the story.'
    },
    {
        image: getAssetUrl('img/step-purchase.svg'),
        title: 'Purchase your book',
        description: 'Opt for a physical copy delivered to your address or choose an instant download to enjoy on tablets and mobile devices.'
    },
];

const categories = [
    {
        link: '/',
        image: getAssetUrl('img/category-fairytale.jpg'),
    },

    {
        link: '/',
        image: getAssetUrl('img/category-scifi.jpg'),
    },

    {
        link: '/',
        image: getAssetUrl('img/category-horror.jpg'),
    },

    {
        link: '/',
        image: getAssetUrl('img/category-superhero.jpg'),
    },
];

export default function Home() {
    return (<>
        <div className="p-4 lg:flex gap-4 justify-between items-center xl:px-0 xl:pt-[49] xl:max-w-[1400px] xl:mx-auto">
            <ul className="flex flex-col gap-4 sm:gap-[50] xl:gap-[107px] lg:w-1/2">
                {steps.map((step, index) => {
                    return <li
                        className="flex gap-4 xl:gap-[33] items-start"
                        key={index}
                    >
                        <Image
                            src={step.image}
                            overrideSrc={step.image}
                            alt=""
                            width={80}
                            height={80}
                        />

                        <div>
                            <strong className="text-[#4c2b53] text-[17px] leading-none sm:text-[28px]">{step.title}</strong>

                            <p className="text-[14px] sm:text-[18px]">{step.description}</p>
                        </div>
                    </li>
                })}
            </ul>

            <Image
                className="hidden lg:block w-1/2 max-w-[662] lg:h-auto"
                src={getAssetUrl('img/steps-banner.png')}
                overrideSrc={getAssetUrl('img/steps-banner.png')}
                alt=""
                width={662}
                height={657}
            />
        </div>

        <div className="text-center mt-4 xl:mt-[97] xl:mb-[105px]">
            <button className="bg-[#16431C] text-[#FFFFFF] w-screen max-w-[537] h-[51] py-[14px] px-4 sm:px-[52px] rounded-[10px] text-[14px] sm:text-[18px]">
                &nbsp;
            </button>
        </div>

        <div className="p-4 pr-2 xl:max-w-[1400px] xl:mx-auto xl:p-0 xl:mb-[182px]">
            <strong className="block text-[#4c2b53] text-xl sm:text-[28px] border-b border-black mr-2 mb-4 pb-2 xl:border-0 xl:mb-[65]">Main Categories:</strong>

            <ul className="grid sm:grid-cols-auto-2fr lg:grid-cols-auto-4fr items-center justify-center gap-2 xl:gap-[108px]">
                {categories.map((category, index) => {
                    return <li key={index}>
                        <Link
                            href={category.link}
                        >
                            <Image
                                src={category.image}
                                overrideSrc={category.image}
                                alt=""
                                width={269}
                                height={269}
                            />
                        </Link>
                    </li>
                })}
            </ul>
        </div>

        <div className="violet-gradient-bg xl:pt-[200] xl:pb-[204]">
            <div className="p-4 lg:flex lg:items-center lg:justify-between lg:gap-4 xl:gap-[133px] xl:max-w-[1400px] xl:mx-auto xl:p-0">
                <Image
                    className="mx-auto mb-4 lg:w-1/2 lg:max-w-[651] xl:w-[651] xl:mx-0"
                    src={getAssetUrl('img/gm-child-banner.jpg')}
                    overrideSrc={getAssetUrl('img/gm-child-banner.jpg')}
                    alt=""
                    width={651}
                    height={651}
                />

                <div className="text-[16px] lg:w-1/2 xl:leading-tight">
                    <p className="xl:flex xl:items-center xl:gap-6 xl:mb-4">
                        <Image
                            className="mx-auto mb-4"
                            src={getAssetUrl('img/logo.svg')}
                            overrideSrc={getAssetUrl('img/logo.svg')}
                            alt=""
                            width={156}
                            height={147}
                        />

                        <strong className="text-[18px]">
                            Let your children dive into a world where they&apos;re
                            not just reading stories - but living them.
                        </strong>
                    </p>

                    <p className="xl:mb-4">
                        Our personalized books transform your child into the hero of their own adventure,
                        and makes each page a magical journey that&apos;s uniquely theirs.<br/>
                        Here&apos;s how our books benefit your little ones:
                    </p>

                    <br /><br />

                    <p className="xl:mb-4">
                        <strong>Boost Self-Esteem:</strong> Children see themselves as capable and brave heroes, which helps build their confidence and self-worth.
                    </p>

                    <p className="xl:mb-4">
                        <strong>Spark Creativity:</strong> Being the star of their own stories encourages children to imagine, explore, and think creatively.
                    </p>

                    <p className="xl:mb-4">
                        <strong>Enhance Reading Skills:</strong> Personalized stories are more engaging, making reading a fun activity that they look forward to, which can improve literacy.
                    </p>

                    <p className="xl:mb-4">
                        <strong>Create Lasting Memories:</strong> Our beautifully crafted books become cherished keepsakes that your child, and even future generations, will treasure.
                    </p>

                    <div className="text-center mt-4 xl:mt-[50]">
                        <button className="bg-[#16431C] text-[#FFFFFF] h-[51] py-[14px] px-4 sm:px-[52] rounded-[10px] text-[14px] sm:text-[18px]">
                            Choose Your Story and Get Started
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </>);
}
