@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --background: #ffffff;
    --foreground: #2B0D2E;
}

@media (prefers-color-scheme: dark) {
    :root {
        --background: #ffffff;
        --foreground: #2B0D2E;
    }
}

body {
    color: var(--foreground);
    background-color: #FFFDF9;
}

.main-header {
    background: linear-gradient(180deg, #DED3B8 0%, #FFFDF9 100%);
}

.yellow-gradient-banner-text {

    font-family: var(--font-playfair);
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    color: #CE9647;

    text-stroke: 2px #2b0d2e;
    -webkit-text-stroke: 2px #2b0d2e;

    @media screen and (min-width: 1280px) {
        font-size: 46px;

        text-stroke: 3px #2b0d2e;
        -webkit-text-stroke: 3px #2b0d2e;

        background: linear-gradient(180deg, #CE9647 0%, #F6EBDC 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.violet-gradient-bg {
    background: linear-gradient(360deg, #DAB9E8 10%, #FFFDF9 100%);
}