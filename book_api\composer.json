{"require": {"guzzlehttp/guzzle": "^7.9", "symfony/routing": "^7.2", "symfony/http-foundation": "^7.2", "symfony/config": "^7.2", "symfony/dependency-injection": "^7.2", "symfony/framework-bundle": "^7.2", "symfony/dotenv": "^7.2", "symfony/yaml": "^7.2", "symfony/console": "^7.2", "symfony/runtime": "^7.2", "symfony/mime": "^7.2", "openai-php/client": "^0.10.3", "symfony/orm-pack": "^2.4", "symfony/maker-bundle": "^1.62", "doctrine/dbal": "^3.9", "doctrine/doctrine-bundle": "^2.13", "mpdf/mpdf": "^8.2", "benbjurstrom/replicate-php": "^0.3.0"}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "config": {"allow-plugins": {"symfony/runtime": true, "php-http/discovery": true}}}