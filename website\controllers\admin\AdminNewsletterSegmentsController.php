<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */
/**
 * Class AdminNewsletterControllerCore
 *
 * @since 1.0.0
 */
class AdminNewsletterSegmentsControllerCore extends AdminController
{

    public $filters = [];

    public $page_header_toolbar_title = 'Contact Segments';

    public function __construct() {

        $this->bootstrap = true;
        $this->table = 'contact_segment';
        $this->className = 'ContactSegment';
        $this->list_id = 'contact_segment';

        $this->addRowAction('edit');
        $this->addRowAction('view');
        $this->addRowAction('delete');

        $this->db = Db::getInstance();
        $this->prefix = _DB_PREFIX_;

        $cookie = new Cookie('psAdmin');
        $this->id_agent = $cookie->id_employee;

        parent::__construct();
        $this->table = 'contact_segment';

        $this->display = 'list';

        $this->fields_list = [
            'id_contact_segment' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'havingFilter' => true,
            ],

            'name' => [
                'title' => $this->l('Name'),
                'havingFilter' => true,
            ],

            'alias' => [
                'title' => $this->l('Alias'),
                'havingFilter' => true,
            ],

            'nb' => [
                'title' => $this->l('Members'),
                'align' => 'center',
                'havingFilter' => true,
            ],
        ];

    }

    public function postProcess() {

        if($this->id_object && Tools::isSubmit('Filters')){
            $filters = Tools::jsonEncode(Tools::getValue('Filters'));

            $this->db->Execute("
                UPDATE {$this->prefix}{$this->table}
                SET filter = '{$filters}'
                WHERE id_contact_segment = '{$this->id_object}'
            ");
        }

    }

    public function renderView() {

        return parent::renderView();

    }

    public function setJS(){
        
        parent::setJS();
        $this->addJS($this->theme_path . '/js/newsletter.js');

    }

    public function renderForm() {

        $filters = Tools::jsonDecode($this->db->getValue("
            SELECT filter FROM {$this->prefix}{$this->table}
            WHERE id_contact_segment = '{$this->id_object}'
        "));

        $fields = $this->db->ExecuteS("

            SELECT
                DISTINCT COLUMN_NAME as name,
                DATA_TYPE as type,
                CASE
                    WHEN NUMERIC_PRECISION != '' THEN NUMERIC_PRECISION
                    ELSE CHARACTER_MAXIMUM_LENGTH
                END as length

            FROM INFORMATION_SCHEMA.COLUMNS

            WHERE TABLE_NAME IN ('{$this->prefix}customer', '{$this->prefix}address') AND COLUMN_NAME NOT LIKE 'id_%'

            ORDER BY COLUMN_NAME ASC

        ");

        Media::addJsDef([
            'filters' => $filters
        ]);

        $this->context->smarty->assign([
            'filter_fields' => $fields
        ]);

        $this->fields_form = [
            'submit' => [
                'title' => $this->l('Save'),
            ],

            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'required' => true,
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Alias'),
                    'name' => 'alias',
                    'required' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'required' => true,
                ],
                [
                    'type' => 'html',
                    'html_content' => $this->createTemplate('controllers/newsletter/segments.tpl')->fetch()
                ],
            ]
        ];

        return parent::renderForm();

    }

    public function renderList() {

        return parent::renderList();

    }
}
