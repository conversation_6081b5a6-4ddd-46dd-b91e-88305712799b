<?php

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    // Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
    // Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    // Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
    // Symfony\Bundle\DebugBundle\DebugBundle::class => ['dev' => true],
    // Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => ['dev' => true, 'test' => true],
    // DAMA\DoctrineTestBundle\DAMADoctrineTestBundle::class => ['test' => true],
    Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle::class => ['all' => true],
    // Doctrine\Bundle\FixturesBundle\DoctrineFixturesBundle::class => ['dev' => true, 'test' => true],
    Symfony\Bundle\MakerBundle\MakerBundle::class => ['dev' => true],
    // Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => ['all' => true],
    // Symfony\UX\TwigComponent\TwigComponentBundle::class => ['all' => true],
    // Symfony\UX\LiveComponent\LiveComponentBundle::class => ['all' => true],
    // Symfony\UX\StimulusBundle\StimulusBundle::class => ['all' => true],
    // Symfonycasts\SassBundle\SymfonycastsSassBundle::class => ['all' => true],
    // Symfony\UX\Icons\UXIconsBundle::class => ['all' => true],
];