<IfModule mod_rewrite.c>
  # That was ONLY to protect you from 500 errors
  # if your server did not have mod_rewrite enabled
  RewriteEngine On

  # NOT needed unless you're using mod_alias to redirect
  # RewriteBase /

  RewriteCond %{REQUEST_URI} !/public

  # Direct all requests to /public folder
  RewriteRule ^(.*)$ public/index.php [L]
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php82” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php82___lsphp .php .php8 .phtml
</IfModule>
# php -- <PERSON><PERSON> cPanel-generated handler, do not edit
