<?php

namespace App\Entity;

use App\Repository\BookPreviewCounterRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BookPreviewCounterRepository::class)]
class BookPreviewCounter
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $ip = null;

    #[ORM\Column(options:["default" => 0])]
    private ?int $preview_count = null;

    #[ORM\Column(nullable: true, type: "datetime")]
    private ?\DateTimeInterface $last_preview = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): static
    {
        $this->ip = $ip;

        return $this;
    }

    public function getPreviewCount(): ?int
    {
        return $this->preview_count;
    }

    public function setPreviewCount(int $preview_count): static
    {
        $this->preview_count = $preview_count;

        return $this;
    }

    public function getLastPreview(): ?\DateTimeInterface
    {
        return $this->last_preview;
    }
    
    public function setLastPreview(\DateTimeInterface $last_preview): self
    {
        $this->last_preview = $last_preview;

        return $this;
    }
}
