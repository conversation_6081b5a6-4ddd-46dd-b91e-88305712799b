<?php

namespace App\Book;

use App\Api\LeonardoClient;
use App\Cache\Cache;
use App\Enums\TitlePosition;
use App\HelperFunctions;
use OpenAI;

class Scene
{
    /**
     * ID of the image to generate
     * 
     * @var string
     */
    public string $generationId = '';

    public $character;

    public function __construct(
        public int $idScene,
        public bool $isCover,
        public string $gender,

        /**
         * @var int[] Contains 2 integers First int is the minimum limit, second int is the maximum limit
         */
        public array $age_group,

        public string $ethnicity,
        public int $seed,
        public string $text,
        public string $prompt,
        public string $template_image,
        public string $character_image,
        public string $style_image,
        public string $overlap_image,
        public string $foreground_image,
        public bool $image_to_image,
        public bool $pose_reference,
        public bool $style_reference,
        public bool $content_reference,
        public bool $character_reference,
        public bool $multi_characters,
        public string $image_to_image_strength,
        public string $pose_reference_strength,
        public string $style_reference_strength,
        public string $content_reference_strength,
        public string $character_reference_strength,
        public string $preset_style,
        public string $static_image,
        public string $negative_prompt,
        public string $outpaint_prompt,
        public int $outpaint_top_size,
        public int $outpaint_bottom_size,
        public int $outpaint_left_size,
        public int $outpaint_right_size,
        public int $outpaint_seed,
        public string $outpaint_top_image,
        public string $outpaint_bottom_image,
        public string $outpaint_left_image,
        public string $outpaint_right_image,
        public string $clothes_top,
        public string $clothes_bottom,
        public int $title_position = (TitlePosition::CENTER)->value
    ) {
        // This is just for defining properties
    }

    /**
     * Replaces the shortcodes on a string with the actual values
     * 
     * @param string $text
     * @param Character $character
     *
     * @return string
     */
    private function replaceShortcodes(string $text, Character $character) {
        $pronoun1 = ($character->gender === 'Girl') ? 'she' : 'he';
        $pronoun2 = ($character->gender === 'Girl') ? 'her' : 'his';
        $pronoun3 = ($character->gender === 'Girl') ? 'her' : 'him';
        $pronoun4 = ($character->gender === 'Girl') ? 'She' : 'He';
        $pronoun5 = ($character->gender === 'Girl') ? 'Her' : 'His';

        $text = str_replace(
            [
                '{PRONOUN1}',
                '{PRONOUN2}',
                '{PRONOUN3}',
                '{PRONOUN4}',
                '{PRONOUN5}',
                '{GENDER}',
                '{BODY_TYPE}',
                '{HAIR}',
                '{HAIR_LENGTH}',
                '{HAIR_STYLE}',
                '{HAIR_COLOR}',
                '{ETHNICITY}'
            ],
            [
                $pronoun1,
                $pronoun2,
                $pronoun3,
                $pronoun4,
                $pronoun5,
                strtolower($character->gender),
                $character->bodyType,
                $character->hair,
                $character->hairLength,
                $character->hairStyle,
                $character->hairColor,
                $character->ethnicity,
            ],

            $text
        );

        return $text;
    }

    /**
     * Asks chatgpt to adapt the prompt to a specific character
     * 
     * @param Character $character
     * 
     * @return void
     */
    protected function adaptPromptToCharacter(Character $character) {
        $client = OpenAI::client(
            $_ENV['OPENAI_API_KEY']
        );

        $character->clothesTop = $this->clothes_top;
        $character->clothesBottom = $this->clothes_bottom;

        $this->character = $character;

        $prompt = 'Character: ' . json_encode($character) . PHP_EOL;
        $prompt .= 'Prompt: ' . $this->prompt . PHP_EOL;
        $prompt .= 'Negative Prompt: ' . $this->negative_prompt;

        try {
            $response = $client->chat()->create([
                'model' => 'gpt-4.1-nano',
    
                'response_format' => [
                    'type' => 'json_object',
                ],
    
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant designed to output JSON. You\'re task is to adjust the given prompt based on the details of the character given to you.
JSON Properties:
- prompt: string
- negative_prompt: string(max length: 1000 characters including spaces)'
                    ],
    
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'text',
                                'text' => $prompt
                            ],
                        ]
                    ],
                ],
            ]);
    
            $responseObj = json_decode($response->choices[0]->message->content);

            if (!$responseObj->prompt || !$responseObj->negative_prompt) {
                throw new \Exception('Couldn\'t adapt prompt to character');
            }

            $this->prompt = $responseObj->prompt;
            $this->negative_prompt = substr($responseObj->negative_prompt, 0, 1000);
        } catch (\Exception $e) {
            throw new \Exception('Couldn\'t adapt prompt to character: ' . $e->getMessage());
        }
    }

    /**
     * Generate an individual image and return generation id
     * 
     * @param \App\Api\LeonardoClient $client
     * @param string $characterImageId
     * @param string $styleImageId
     * @param string $templateImageId
     * @param \App\Book\Character $character
     * @param bool $isPreview
     * 
     * @return Scene
     */
    public function generateImage(
        LeonardoClient $client,
        string $characterImageId,
        string $styleImageId,
        string $templateImageId,
        Character $character,
        bool $isPreview
    ): Scene {
        $cacheKey = HelperFunctions::hashFileInput(
            $character->imagePath,
            $this->idScene,
            $character->ethnicity,
            $character->hairStyle,
            $character->hairLength
        );

        if ($isPreview && Cache::exists($cacheKey)) {
            $savedObject = Cache::getObject($cacheKey);

            $savedScene = new Scene(
                $savedObject->idScene,
                $savedObject?->isCover ?? false,
                $savedObject->gender,
                $savedObject->age_group,
                $savedObject->ethnicity,
                $savedObject->seed,
                $savedObject->text,
                $savedObject->prompt,
                $savedObject->template_image,
                $savedObject->character_image,
                $savedObject->style_image,
                $savedObject->overlap_image,
                $savedObject->foreground_image,
                $savedObject->image_to_image,
                $savedObject->pose_reference,
                $savedObject->style_reference,
                $savedObject->content_reference,
                $savedObject->character_reference,
                $savedObject->multi_characters,
                $savedObject->image_to_image_strength,
                $savedObject->pose_reference_strength,
                $savedObject->style_reference_strength,
                $savedObject->content_reference_strength,
                $savedObject->character_reference_strength,
                $savedObject->preset_style,
                $savedObject->static_image,
                $savedObject->negative_prompt,
                $savedObject->outpaint_prompt,
                $savedObject->outpaint_top_size,
                $savedObject->outpaint_bottom_size,
                $savedObject->outpaint_left_size,
                $savedObject->outpaint_right_size,
                $savedObject->outpaint_seed,
                $savedObject->outpaint_top_image ?? '',
                $savedObject->outpaint_bottom_image ?? '',
                $savedObject->outpaint_left_image ?? '',
                $savedObject->outpaint_right_image ?? '',
                $savedObject->clothes_top,
                $savedObject->clothes_bottom,
                $savedObject?->title_position ?? (TitlePosition::CENTER)->value
            );

            $savedScene->generationId = $savedObject->generationId;

            return $savedScene;
        }

        $this->adaptPromptToCharacter($character);

        $controlnets = [];
        if ($this->character_reference && $characterImageId) {
            $controlnets[] = [
                'initImageId' => $characterImageId,
                'initImageType' => 'UPLOADED',
                'preprocessorId' => 133,
                'strengthType' => $this->character_reference_strength,
            ];
        }

        if ($this->style_reference && $styleImageId) {
            $controlnets[] = [
                'initImageId' => $styleImageId,
                'initImageType' => 'UPLOADED',
                'preprocessorId' => 67,
                'strengthType' => $this->style_reference_strength,
            ];
        }

        if ($this->pose_reference && $templateImageId) {
            $controlnets[] = [
                'initImageId' => $templateImageId,
                'initImageType' => 'UPLOADED',
                'preprocessorId' => 21,
                'weight' => (float) $this->pose_reference_strength,
            ];

            $controlnets[] = [
                'initImageId' => $templateImageId,
                'initImageType' => 'UPLOADED',
                'preprocessorId' => 19,
                'weight' => 0.3,
            ];
        }

        if ($this->content_reference && $templateImageId) {
            $controlnets[] = [
                'initImageId' => $templateImageId,
                'initImageType' => 'UPLOADED',
                'preprocessorId' => 100,
                'strengthType' => $this->content_reference_strength,
            ];
        }

        $data = [
            // 'sd_version' => 'SDXL_0_9',
            'modelId' => '2067ae52-33fd-4a82-bb92-c2c55e7d2786',
            'seed' => $this->seed,
            'height' => 1024,
            'width' => 1024,
            'num_images' => 1,
            'guidance_scale' => 7,

            'prompt' => $this->replaceShortcodes($this->prompt, $character),
            'negative_prompt' => $this->negative_prompt,
            'presetStyle' => $this->preset_style,
            'scheduler' => 'LEONARDO',
            'public' => false,
            'tiling' => false,

            'controlnets' => $controlnets,

            'styleUUID' => '726585f5-d231-4a3a-bee9-9a5e5d3df779',
        ];

        if (!$isPreview) {
            // We only serve high quality photos when not in preview
            $data['height'] = 1024;
            $data['width'] = 1024;
        }

        if ($this->image_to_image && $templateImageId) {
            $data['init_image_id'] = $templateImageId;
            $data['init_strength'] = (float) $this->image_to_image_strength;
        }

        $response = $client->post(
            'generations',

            [
                'json' => $data,
            ]
        );

        $this->generationId = $response['sdGenerationJob']['generationId'];

        if (!$this->generationId) {
            throw new \Exception('Scene Generation ID not found');
        }

        Cache::cacheObject($cacheKey, $this);

        return $this;
    }
}
