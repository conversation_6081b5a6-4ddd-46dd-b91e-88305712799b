<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class PrestaShopExceptionCore
 *
 * @since 1.0.0
 */
class PrestaShopExceptionCore extends Exception
{
    protected $trace;

    const FILE_CONTEXT_LINES = 30;

    /**
     * PrestaShopExceptionCore constructor.
     *
     * @param string         $message
     * @param int            $code
     * @param Exception|null $previous
     * @param array|null     $customTrace
     * @param string|null    $file
     * @param int|null       $line
     */
    public function __construct($message = '', $code = 0, Exception $previous = null, $customTrace = null, $file = null, $line = null)
    {
        parent::__construct($message, (int) $code, $previous);

        if (!$customTrace) {
            $this->trace = $this->getTrace();
        } else {
            $this->trace = $customTrace;
        }

        if ($file) {
            $this->file = $file;
        }
        if ($line) {
            $this->line = $line;
        }
    }

    /**
     * This method acts like an error handler, if dev mode is on, display the error else use a better silent way
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function displayMessage()
    {
        header('HTTP/1.1 500 Internal Server Error');
        header('Content-Type: text/html');

        //clean any output buffer there might be
        while (ob_get_level()) {
            ob_end_clean();
        }

        // generate array describing this exception
        $errorDescription = $this->getErrorDescription();

        if (_PS_MODE_DEV_ || getenv('CI') || (defined('TESTS_RUNNING') && TESTS_RUNNING)) {
            // in debug mode, we can render debug page
            echo static::renderDebugPage($errorDescription);
        } else {
            // in production mode, we will serialize and encrypt error description, and display
            // generic error500 page. Visitor can download encrypted error and send it to
            // merchant for investigation
            try {
                $encrypted = Encryptor::getInstance()->encrypt(json_encode($errorDescription));
            } catch (PrestaShopException $e) {
                $encrypted = false;
            }
            echo static::displayErrorTemplate(_PS_ROOT_DIR_.'/error500.phtml', [
                'encrypted' => $encrypted,
                'shopEmail' => Configuration::get('PS_SHOP_EMAIL'),
            ]);
        }
        // Log the error to the disk
        $this->logError();
        exit;
    }

    /**
     * $this method can be overridden by subclasses to include additional sections into output
     * See PrestaShopDatabaseException for example how to add new section displaying SQL query
     *
     * @return array
     */
    protected function getExtraSections() {
        return [];
    }

    /**
     * Helper method to render debug page from $errorDescription array generated by
     * getErrorDescription method. This method can be used either to directly render
     * error page (dev mode), or to display encrypted error
     *
     * @param $errorDescription array
     * @return string html page
     */
    public static function renderDebugPage($errorDescription)
    {
        return static::displayErrorTemplate(_PS_ROOT_DIR_.'/error500_debug.phtml', $errorDescription);
    }

    /**
     * Reads $file from disk, and returns $total lines around $line. Result is an array
     * of arrays, with information about line number in file, if the line is highlighted,
     * and actual line
     *
     * @param $file string input file
     * @param $line integer index of line in the file. This line will be highlighted
     * @param $total integer total number of lines to read. Pass zero to return all lines
     *
     * @return array|null
     */
    protected static function readFile($file, $line, $total) {
        if (! file_exists($file)) {
            return null;
        }
        $lines = (array) file($file);
        if ($total > 0) {
            $third = (int)($total / 3);
            $offset = $line - (2 * $third);
            if ($offset < 0) {
                $offset = 0;
            }
            $lines = array_slice($lines, $offset, $total);
        } else {
            $offset = 0;
        }

        $ret = [];
        foreach ($lines as $k => $l) {
            $number = $offset + $k + 1;
            $ret[] = [
                'number' => $number,
                'highlighted' => $number === $line,
                'line' => $l
            ];
        }
        return $ret;
    }

    /**
     * This method generates array that describes this exception. This description can be used to
     * display debug page. It can also be serialized
     *
     * @return array exception description
     */
    public function getErrorDescription()
    {
        $smartyTrace = SmartyCustom::$trace;

        $file = $this->getFile();
        if (SmartyCustom::isCompiledTemplate($file)) {
            $file = array_pop($smartyTrace);
            $fileType = 'smarty';
            $line = 0;
            $fileContent = static::readFile($file, 0, -1);
        } else {
            $fileType = 'php';
            $line = $this->getLine();
            $fileContent = static::readFile($file, $line, static::FILE_CONTEXT_LINES);
        }

        $stacktrace = [];
        foreach ($this->trace as $id => $trace) {
            $class = isset($trace['class']) ? $trace['class'] : '';
            $function = isset($trace['function']) ? $trace['function'] : '';
            $type = isset($trace['type']) ? $trace['type'] : '';
            $fileName = isset($trace['file']) ? $trace['file'] : '';
            $lineNumber = isset($trace['line']) ? $trace['line'] : 0;
            $args = isset($trace['args']) ? $trace['args'] : [];
            $isTemplate = false;
            $showLines = static::FILE_CONTEXT_LINES;
            if (SmartyCustom::isCompiledTemplate($fileName)) {
                $isTemplate = true;
                $fileName = array_pop($smartyTrace);
                $lineNumber = 0;
                $showLines = -1;
            }
            $relativeFile = static::getRelativeFile($fileName);
            $nextId = $id + 1;
            $currentFunction = '';
            $currentClass = '';
            if (isset($this->trace[$nextId]['class'])) {
                $currentClass = $this->trace[$nextId]['class'];
                $currentFunction = $this->trace[$nextId]['function'];
            }
            $stacktrace[] = [
                'class' => $class,
                'function' => $function,
                'type' => $type,
                'fileType' => $isTemplate ? 'template' : 'php',
                'fileName' => $relativeFile,
                'line' => $lineNumber,
                'args' => array_map([$this, 'displayArgument'], $args),
                'fileContent' => static::readFile($fileName, $lineNumber, $showLines),
                'description' => static::describeOperation($class, $function, $args),
                'suppressed' => static::isSuppressed($relativeFile, $currentClass, $currentFunction, $class, $function)
            ];
        }

        return [
            // 'errorName' => str_replace('PrestaShop', 'ThirtyBees', get_class($this)),
            'errorName' => 'ShopTechException',
            'errorMessage' => $this->getMessage(),
            'fileType' => $fileType,
            'fileName' => static::getRelativeFile($file),
            'line' => $line,
            'fileContent' => $fileContent,
            'stacktrace' => $stacktrace,
            'extraSections' => $this->getExtraSections()
        ];
    }

    /**
     * Method will render argument into string. Similar to var_dump, but will product smaller output
     *
     * @param $variable variable to be rendered
     * @param int $strlen max length of string. If longer then string will be truncated and ... will be added
     * @param int $width maximal number of array items to be rendered
     * @param int $depth maximaln depth that we will traverse
     * @param int $i current depth
     * @param array $objects array of seen objects
     *
     * @return string
     */
    protected function displayArgument($variable, $strlen = 80, $width = 50, $depth = 2, $i = 0, $objects = [])
    {
        $search = array("\0", "\a", "\b", "\f", "\n", "\r", "\t", "\v");
        $replace = array('\0', '\a', '\b', '\f', '\n', '\r', '\t', '\v');

        switch (gettype($variable)) {
            case 'boolean':
                return $variable ? 'true' : 'false';
            case 'integer':
            case 'double':
                return (string)$variable;
            case 'resource':
                return '[resource]';
            case 'NULL':
                return 'null';
            case 'unknown type':
                return '???';
            case 'string':
                $len = strlen($variable);
                $variable = str_replace($search, $replace, substr($variable,0,$strlen),$count);
                $variable = substr($variable,0, $strlen);
                if ($len<$strlen) {
                    return '"'.$variable.'"';
                } else {
                    return 'string('.$len.'): "'.$variable.'"...';
                }
            case 'array':
                $len = count($variable);
                if ($i == $depth) {
                    return 'array('.$len.') [...]';
                }
                if (!$len) {
                    return 'array(0) []';
                }
                $string = '';
                $keys = array_keys($variable);
                $spaces = str_repeat(' ',$i*2);
                $string.= "array($len)\n".$spaces.'[';
                $count=0;
                foreach($keys as $key) {
                    if ($count==$width) {
                        $string.= "\n".$spaces."  ...";
                        break;
                    }
                    $string.= "\n".$spaces."  [$key] => ";
                    $string.= $this->displayArgument($variable[$key], $strlen, $width, $depth,$i+1, $objects);
                    $count++;
                }
                $string.="\n".$spaces.']';
                return $string;
            case 'object':
                $id = array_search($variable, $objects,true);
                if ($id !== false) {
                    return get_class($variable) . '#' . ($id + 1) . ' {...}';
                }
                if ($i==$depth) {
                    return get_class($variable).' {...}';
                }
                $string = '';
                $id = array_push($objects, $variable);
                $array = (array)$variable;
                $spaces = str_repeat(' ',$i*2);
                $string.= get_class($variable)."#$id\n".$spaces.'{';
                $properties = array_keys($array);
                foreach($properties as $property) {
                    $name = str_replace("\0",':', trim($property));
                    $string .= "\n".$spaces."  [$name] => ";
                    $string .= $this->displayArgument($array[$property], $strlen, $width, $depth,$i+1, $objects);
                }
                $string .= "\n".$spaces.'}';
                return $string;
            default:
                return print_r($variable, true);
        }
    }

    /**
     * Log the error on the disk
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    protected function logError()
    {
        $logger = new FileLogger();
        $logger->setFilename(_PS_ROOT_DIR_.'/log/'.date('Ymd').'_exception.log');
        $logger->logError($this->getExtendedMessage());
    }

    /**
     * @deprecated 2.0.0
     */
    protected function getExentedMessage($html = true)
    {
        Tools::displayAsDeprecated();

        return $this->getExtendedMessage();
    }

    /**
     * Return the content of the Exception
     * @return string content of the exception.
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    protected function getExtendedMessage($html = true)
    {
        if (SmartyCustom::isCompiledTemplate($this->file)) {
            return $this->getMessage() . ' in template file ' . static::getRelativeFile(SmartyCustom::getCurrentTemplate());
        } else {
            return $this->getMessage() . ' at line ' . $this->getLine() . ' in file ' . static::getRelativeFile($this->file);
        }
    }

    /**
     * Returns file path relative to shoptech root
     *
     * @param $file
     * @return string
     */
    protected static function getRelativeFile($file)
    {
        if ($file) {
            return ltrim(str_replace([_PS_ROOT_DIR_, '\\'], ['', '/'], $file), '/');
        } else {
            return '';
        }
    }

    /**
     * Display a phtml template file
     *
     * @param string $file
     * @param array  $params
     *
     * @return string Content
     *
     * @since 1.0.0
     */
    protected static function displayErrorTemplate($file, $params = [])
    {
        foreach ($params as $name => $param) {
            $$name = $param;
        }

        ob_start();

        include($file);

        $content = ob_get_contents();
        if (ob_get_level() && ob_get_length() > 0) {
            ob_end_clean();
        }

        return $content;
    }

    /**
     * Helper method to downplay some entries from stacktrace. Some entries from stacktrace
     * will be greyed out, and displayed with smaller font, so it does not distract reader
     * when investigating source of error
     *
     * @param $relativePath string relative path of file
     * @param $class string current classname
     * @param $function string currently evaluating function
     * @param $calledClass string class that's being called
     * @param $calledFunction string function being called
     *
     * @return bool if this entry should be suppressed
     */
    private static function isSuppressed($relativePath, $class, $function, $calledClass, $calledFunction)
    {
        // suppress any entries that calls following methods
        $suppressCalls = [
            [ 'DispatcherCore',  'dispatch' ],
            [ 'Smarty_Custom_Template', 'fetch' ],
            [ 'ControllerCore', 'run' ]
        ];
        foreach ($suppressCalls as $callable) {
            if ($callable[0] === $calledClass && $callable[1] === $calledFunction) {
                return true;
            }
        }

        // suppress these methods
        $suppressMethods = [
            [ 'DispatcherCore',  'dispatch' ],
            [ 'DbCore', 'execute' ],
            [ 'DbCore', 'query' ],
            [ 'Smarty_Custom_Template', 'fetch' ],
            [ 'ControllerCore', 'run' ],
            [ 'HookCore', 'exec' ],
            [ 'HookCore', 'execWithoutCache' ],
            [ 'HookCore', 'coreCallHook' ]
        ];
        foreach ($suppressMethods as $callable) {
            if ($callable[0] === $class && $callable[1] === $function) {
                return true;
            }
        }

        // suppress any entries if filepath starts with following substring
        $paths = [
            'vendor/',
            'classes/SmartyCustom.php',
            'config/smarty'
        ];
        foreach ($paths as $match) {
            if (strpos($relativePath, $match) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Helper method to describe special functions in shoptech codebase, such as
     * method to include sub-template from within smarty, or smarty function to trigger
     * hook.
     *
     * This makes the stacktrace more readable
     *
     * @param $class string class name
     * @param $function string called function
     * @param $args array parameters passed to $class::$function() method
     *
     * @return string | null
     */
    private static function describeOperation($class, $function, $args)
    {
        if ($class === 'Smarty_Internal_Template' && $function === 'getSubTemplate') {
            $templateName = isset($args['0']) && is_string($args['0']) ? static::getRelativeFile($args['0']) : '';
            return 'Include sub-template <b>' . $templateName . '</b>';
        }
        if (!$class && $function === 'smartyHook') {
            $hookName = isset($args[0]['h']) ? $args[0]['h'] : '';
            return 'Execute hook <b>' . $hookName . '</b>';
        }
    }

}
