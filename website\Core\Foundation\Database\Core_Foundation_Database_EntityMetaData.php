<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class Core_Foundation_Database_EntityMetaData
 *
 * @since 1.0.0
 */
// @codingStandardsIgnoreStart
class Core_Foundation_Database_EntityMetaData
{
    // @codingStandardsIgnoreEnd

    protected $tableName;
    protected $primaryKeyFieldnames;

    /**
     * @param string $name
     *
     * @return Core_Foundation_Database_EntityMetaData $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setTableName($name)
    {
        $this->tableName = $name;

        return $this;
    }

    /**
     * @return string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getTableName()
    {
        return $this->tableName;
    }

    /**
     * @param array $primaryKeyFieldnames
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setPrimaryKeyFieldNames(array $primaryKeyFieldnames)
    {
        $this->primaryKeyFieldnames = $primaryKeyFieldnames;

        return $this;
    }

    /**
     * @return mixed
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getPrimaryKeyFieldnames()
    {
        return $this->primaryKeyFieldnames;
    }

    /**
     * @param string $entityClassName
     *
     * @return $this
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function setEntityClassName($entityClassName)
    {
        $this->entityClassName = $entityClassName;

        return $this;
    }

    /**
     * @return mixed
     *
     * @return string
     *
     * @since 1.0.0
     * @version 1.0.0 Initial version
     */
    public function getEntityClassName()
    {
        return $this->entityClassName;
    }
}
