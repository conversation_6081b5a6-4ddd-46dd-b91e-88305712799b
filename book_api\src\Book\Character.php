<?php

namespace App\Book;

use OpenAI;

class Character
{
    /**
     * @var string $imagePath
     */
    public string $imagePath;

    /**
     * @var string
     */
    public string $gender;

    /**
     * @var int
     */
    public int $age;

    /**
     * @var string
     */
    public string $ethnicity = '';

    /**
     * @var string
     */
    public string $bodyType = 'Average';

    /**
     * @var string
     */
    public string $hair;

    /**
     * @var string
     */
    public string $hairLength = 'Long';

    /**
     * @var string
     */
    public string $hairStyle = 'Straight';

    /**
     * @var string
     */
    public string $skinColor = 'Fair';

    /**
     * @var string
     */
    public string $hairColor = 'Black';

    /**
     * @var string
     */
    public string $clothesTop = '';

    /**
     * @var string
     */
    public string $clothesBottom = '';

    private function imageToBaseUri($imagePath) {
        if (!file_exists($imagePath)) {
            return '';
        }

        // Get the file type (MIME type)
        $fileInfo = getimagesize($imagePath);
        if ($fileInfo === false) {
            return false; // Not a valid image
        }

        $mimeType = $fileInfo['mime'];

        // Read the image file contents
        $imageData = file_get_contents($imagePath);
        if ($imageData === false) {
            return false; // Error reading the file
        }

        // Encode the image data to Base64
        $base64Image = base64_encode($imageData);

        // Return the formatted Data URL
        $imageData = "data:$mimeType;base64,$base64Image";

        return $imageData;
    }

    /**
     * Uses OPENAI API to describe the character in the image and
     * populate fields
     * 
     * @param string $imagePath
     */
    public function useImage($imagePath) {
        if (!file_exists($imagePath)) {
            throw new \Exception('Image not found');
        }

        $imageData = $this->imageToBaseUri($imagePath);

        $client = OpenAI::client(
            $_ENV['OPENAI_API_KEY']
        );

        $prompt = 'Can you describe me the ethnicity, hair color, hair length and hair style of this kid. Also the skin color.';

        $response = $client->chat()->create([
            'model' => 'gpt-4.1-nano',
            'seed' => 1,
            'response_format' => [
                'type' => 'json_object',
            ],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a helpful assistant designed to output JSON. Reply conversationally on each property of the JSON object.
Example:
{
    ethnicity: Asian
    hair_color: Naturally black
    hair_length: Long
    hair_style: Soft, neatly combed, with a clear side part; the hair is fully loose and not styled with any products or accessories.
    skin_color: Warm medium tan complexion
}

Supported Ehtnicities: African American, White, Asian, Arab, Latino, Latina, Mixed, Jewish'
                ],

                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt
                        ],

                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $imageData,
                            ],
                        ]
                    ]
                ],
            ],
        ]);

        $responseObj = json_decode($response->choices[0]->message->content);

        $this->ethnicity = $responseObj->ethnicity ?? '';
        $this->bodyType = $responseObj->body_type ?? 'Average';
        $this->hair = $responseObj->hair_color . ', ' . $responseObj->hair_length . ', ' . $responseObj->hair_style;
        $this->hairColor = $responseObj->hair_color ?? '';
        $this->hairLength = $responseObj->hair_length ?? '';
        $this->hairStyle = $responseObj->hair_style ?? '';
        $this->skinColor = $responseObj->skin_color ?? '';
        $this->imagePath = $imagePath;
    }
}