<?php

namespace App\Entity;

use App\Repository\ImageWebhookRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ImageWebhookRepository::class)]
class ImageWebhook
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $image_path = null;

    #[ORM\Column(type: Types::TEXT, unique: true)]
    private ?string $generation_id = null;

    #[ORM\Column]
    private ?bool $is_processing = null;

    #[ORM\Column]
    private ?bool $is_processed = null;

    #[ORM\Column(length: 255)]
    private ?string $source = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getImagePath(): ?string
    {
        return $this->image_path;
    }

    public function setImagePath(string $image_path): static
    {
        $this->image_path = $image_path;

        return $this;
    }

    public function getGenerationId(): ?string
    {
        return $this->generation_id;
    }

    public function setGenerationId(string $generation_id): static
    {
        $this->generation_id = $generation_id;

        return $this;
    }

    public function isProcessing(): ?bool
    {
        return $this->is_processing;
    }

    public function setIsProcessing(bool $is_processing): static
    {
        $this->is_processing = $is_processing;

        return $this;
    }

    public function isProcessed(): ?bool
    {
        return $this->is_processed;
    }

    public function setIsProcessed(bool $is_processed): static
    {
        $this->is_processed = $is_processed;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(string $source): static
    {
        $this->source = $source;

        return $this;
    }
}
