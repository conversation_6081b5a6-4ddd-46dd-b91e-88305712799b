<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */
/**
 * Class AdminNewsletterControllerCore
 *
 * @since 1.0.0
 */
class AdminNewsletterContentControllerCore extends AdminController
{

    public $name = 'newsletter_content';
    public $displayName = 'Newsletter Content';

    public $filters = [];

    public function __construct() {

        $this->bootstrap = true;
        $this->table = 'newsletter_content';
        $this->className = 'NewsletterContent';
        $this->list_id = 'newsletter_content';

        $this->addRowAction('edit');
        $this->addRowAction('view');
        $this->addRowAction('delete');

        $this->db = Db::getInstance();
        $this->prefix = _DB_PREFIX_;

        parent::__construct();

        $this->display = 'list';

        $this->fields_list = [
            'id_newsletter_content' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'havingFilter' => true,
            ],

            'name' => [
                'title' => $this->l('Name'),
                'havingFilter' => true,
            ],
        ];

    }

    public function postProcess() {

        parent::postProcess();

    }

    public function renderView() {

        return parent::renderView();

    }

    public function setJS(){
        
        parent::setJS();

    }

    public function renderForm() {

        $this->fields_form = [
            'submit' => [
                'title' => $this->l('Save'),
            ],

            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'required' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'content',
                    'required' => true,
                    'autoload_rte' => true
                ],
            ]
        ];

        return parent::renderForm();

    }

    public function renderList() {

        return parent::renderList();

    }
}
