<?php

namespace App\Service\Image;

use App\HelperFunctions;
use BenBjurstrom\Replicate\Replicate;
use App\Service\Image\OutpaintSettings;

class PostProcess
{
    private static function importAndSaveImage(string $imageUrl, string $savePath): array {
        // Initialize cURL session
        $ch = curl_init($imageUrl);
        
        // Set cURL options
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        // Execute cURL session
        $imageData = curl_exec($ch);
        
        // Check for errors
        if (curl_errno($ch)) {
            curl_close($ch);
            return ["success" => false, "error" => curl_error($ch)];
        }
        
        // Get HTTP response code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ["success" => false, "error" => "Failed to download image, HTTP Code: $httpCode"];
        }
        
        // Save the image to the specified path
        if (file_put_contents($savePath, $imageData)) {
            return ["success" => true, "message" => "Image saved successfully to $savePath"];
        } else {
            return ["success" => false, "error" => "Failed to save the image."];
        }
    }

    /**
     * Upscales images to 3 times its size
     * 
     * @param string $imagePath
     * @param string $outputPath
     * @param OutpaintSettings $settings
     * 
     * @return string Path to upscaled image
     */
    public static function outpaint(
        string $imagePath,
        string $outputPath,
        OutpaintSettings $settings,
    ): string
    {
        if ($outputPath === $imagePath) {
            throw new \Exception('Output path cannot be the same as input path');
        }

        if (file_exists($outputPath)) {
            return $outputPath;
        }

        if (
            $settings->topImage ||
            $settings->bottomImage ||
            $settings->leftImage ||
            $settings->rightImage
        ) {
            // We already have the images needed for outpainting no need to get from replicate
            return PostProcess::composeOutpaintingGD($imagePath, $settings, $outputPath);
        }

        $api = new Replicate(
            apiToken: $_ENV['REPLICATE_API_TOKEN'],
        );

        $imageName = basename($imagePath);
        $imageUrl = 'https://book.magipa.shoptech.media/public/generations/' . $imageName;

        $version = '377564d35153c66f8629d9540480813685d114f0552e9a3c9ffe5dd315091e68';
        $input = [
            "image" => $imageUrl,
            "prompt" => $settings->prompt,
            "negative_prompt" => "ugly",
            "output_format" => "jpg",
            "output_quality" => 80,
            "steps" => 20,
            "cfg" => 4,
            "top" => $settings->top,
            "left" => $settings->left,
            "seed" => $settings->seed,
            "right" => $settings->right,
            "bottom" => $settings->bottom
        ];

        $data = $api->predictions()->create($version, $input);

        $output = null;

        while ($output === null) {
            sleep(1);

            $data = $api->predictions()->get($data->id);

            $output = $data->output;

            if ($data->error) {
                $error = $data->error;

                if (is_array($error)) {
                    $error = json_encode($error);
                }

                throw new \Exception($error);
            }

            if ($output) {
                self::importAndSaveImage($output[0], $outputPath);
                break;
            }
        }

        return $outputPath;
    }

    /**
     * Overlaps the given image over the template image. Do not overwrite the template image
     * 
     * @param string $imagePath Path to the image. This image has a transparent background
     * @param string $templateImage Path to the template image. This image is a jpg
     * 
     * @return string Path to the final image
     */
    public static function overlapTemplateImage($imagePath, $templateImage, $outputPath) {
        if (!file_exists($imagePath)) {
            throw new \Exception('Overlap Template Image: Image does not exist');
        }

        if (!file_exists($templateImage)) {
            throw new \Exception('Overlap Template Image: Template image does not exist');
        }

        try {
            // Get extension of file
            $info = getimagesize($imagePath);
            $ext = $info['mime'];

            if ($ext === 'image/webp') {
                $imageObj = imagecreatefromwebp($imagePath);
            } elseif ($ext === 'image/png') {
                $imageObj = imagecreatefrompng($imagePath);
            } else {
                $imageObj = imagecreatefromjpeg($imagePath);
            }

            // Get extension of file
            $info = getimagesize($templateImage);
            $ext = $info['mime'];

            if ($ext === 'image/webp') {
                $templateImageObj = imagecreatefromwebp($templateImage);
            } elseif ($ext === 'image/png') {
                $templateImageObj = imagecreatefrompng($templateImage);
            } else {
                $templateImageObj = imagecreatefromjpeg($templateImage);
            }

            imagealphablending($imageObj, true);
            imagesavealpha($imageObj, true);
            imagecopy(
                $templateImageObj,
                $imageObj,
                0,
                0,
                0,
                0,
                1024,
                1024
            );
        } catch (\Exception $e) {
            echo $e->getMessage();
            throw new \Exception('Overlap Template Image: ' . $e->getMessage());
        }

        imagepng($templateImageObj, $outputPath, 0);

        return $outputPath;
    }

    /**
     * Removes the background of the given image
     * 
     * @param string $imagePath Path to the image. This is a jpg
     * @param string $outputPath Path to the final image. Saved as a png
     * 
     * @return string Path to the final image after removing the background. Saved as a png
     */
    public static function removeBg($imagePath, $outputPath) {
        if (!file_exists($imagePath)) {
            throw new \Exception('Remove BG: Image does not exist');
        }

        if (file_exists($outputPath)) {
            return $outputPath;
        }

        $api = new Replicate(
            apiToken: $_ENV['REPLICATE_API_TOKEN'],
        );

        $imageUrl = str_replace(
            rtrim(HelperFunctions::getRootDir(), '/'),
            'https://book.magipa.shoptech.media',
            $imagePath
        );

        $version = 'f74986db0355b58403ed20963af156525e2891ea3c2d499bfbfb2a28cd87c5d7';
        $input = [
            "image" => $imageUrl,
            'resolution' => ''
        ];

        $data = $api->predictions()->create($version, $input);

        $output = null;

        while ($output === null) {
            sleep(1);

            $data = $api->predictions()->get($data->id);

            $output = $data->output;

            if ($data->error) {
                $error = $data->error;

                if (is_array($error)) {
                    $error = json_encode($error);
                }

                throw new \Exception($error);
            }

            if ($output) {
                self::importAndSaveImage($output, $outputPath);
                break;
            }
        }

        return $outputPath;
    }

    /**
     * Upscales images to 3 times its size
     * 
     * @param string $imagePath
     * @param string $outputPath
     * 
     * @return string Path to upscaled image
     */
    public static function upscale(string $imagePath, string $outputPath): string
    {
        $api = new Replicate(
            apiToken: $_ENV['REPLICATE_API_TOKEN'],
        );

        $imageName = basename($imagePath);
        $imageUrl = 'https://book.magipa.shoptech.media/public/generations/' . $imageName;

        $version = '4f7eb3da655b5182e559d50a0437440f242992d47e5e20bd82829a79dee61ff3';
        $input = [
            'model' => 'alexgenovese/upscaler',
            'image' => $imageUrl,
            'scale' => 3,
            'face_enhance' => true,
        ];

        $data = $api->predictions()->create($version, $input);

        $output = null;

        while ($output === null) {
            sleep(1);

            $data = $api->predictions()->get($data->id);

            $output = $data->output;

            if ($output) {
                self::importAndSaveImage($output, $outputPath);
                break;
            }
        }

        return $outputPath;
    }

    /**
     * Compose an out-painted image with PHP-GD.
     *
     * @param string   $basePath    Path to the centre/original image.
     * @param OutpaintSettings $settings    ->topImage / ->bottomImage / ->leftImage / ->rightImage (path or null)
     * @param string   $outputPath  Path where the merged file will be saved.
     * 
     * @return string
     *
     * @throws RuntimeException     On unreadable files or unsupported formats.
     */
    public static function composeOutpaintingGD(
        string $basePath,
        OutpaintSettings $settings,
        string $outputPath
    ): string
    {
        // Hexcode for white. Prefixed with 0x
        $bgColorHex = 0xFFFFFF;

        // -------- helpers --------
        $load = static function (string $path) {
            $info = getimagesize($path);
            $ext = $info['mime'];

            switch ($ext) {
                case 'image/png':  return imagecreatefrompng($path);
                case 'image/jpg':
                case 'image/jpeg': return imagecreatefromjpeg($path);
                case 'image/webp': return function_exists('imagecreatefromwebp') ? imagecreatefromwebp($path) : null;
                default:     return null;
            }
        };

        $save = static function ($im, string $path) {
            $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));
            switch ($ext) {
                case 'png':  return imagepng($im, $path, 9);
                case 'jpg':
                case 'jpeg': return imagejpeg($im, $path, 90);
                case 'webp': return function_exists('imagewebp') ? imagewebp($im, $path, 90) : false;
                default:     throw new \RuntimeException("Unsupported output format: $ext");
            }
        };

        if (!is_readable($basePath)) {
            throw new \RuntimeException("Base image not found: {$basePath}");
        }

        // -------- load what’s present --------
        $base   = $load($basePath);
        $top    = $settings->topImage    ? $load($settings->topImage)    : 0;
        $bottom = $settings->bottomImage ? $load($settings->bottomImage) : 0;
        $left   = $settings->leftImage   ? $load($settings->leftImage)   : 0;
        $right  = $settings->rightImage  ? $load($settings->rightImage)  : 0;

        // nothing to stitch → just copy
        if (!$top && !$bottom && !$left && !$right) {
            return $basePath;
        }

        // -------- sizes --------
        $bw = imagesx($base);
        $bh = imagesy($base);

        $tw = $top    ? imagesx($top)    : $bw;
        $th = $top    ? imagesy($top)    : 0;
        $bw2 = $bottom ? imagesx($bottom) : $bw;
        $bh2 = $bottom ? imagesy($bottom) : 0;
        $lw = $left   ? imagesx($left)   : 0;
        $rw = $right  ? imagesx($right)  : 0;

        $canvasWidth  = $lw + $bw + $rw;
        $canvasHeight = $th + $bh + $bh2;

        // -------- create canvas --------
        $canvas = imagecreatetruecolor($canvasWidth, $canvasHeight);

        // Flatten image to replace transparency with white background
        $red = ($bgColorHex >> 16) & 0xFF;
        $green = ($bgColorHex >> 8)  & 0xFF;
        $blue =  $bgColorHex        & 0xFF;
        $bg = imagecolorallocate($canvas, $red, $green, $blue);
        imagefill($canvas, 0, 0, $bg);

        // -------- composite strips, then centre --------
        if ($top) {
            imagecopy(
                $canvas,
                $top,
                $lw,
                0,
                0,
                0,
                $tw,
                $th
            );
        }

        if ($bottom) {
            imagecopy(
                $canvas,
                $bottom,
                $lw,
                $th + $bh,
                0,
                0,
                $bw2,
                $bh2
            );
        }

        if ($left) {
            imagecopy(
                $canvas,
                $left,
                0,
                $th,
                0,
                0,
                $lw,
                imagesy($left)
            );
        }

        if ($right) {
            imagecopy(
                $canvas,
                $right,
                $lw + $bw,
                $th,
                0,
                0,
                $rw,
                imagesy($right)
            );
        }

        // Add original image last, so it sits on top, thus always visible
        imagecopy(
            $canvas,
            $base,
            $lw,
            $th,
            0,
            0,
            $bw,
            $bh
        );

        // -------- save & clean --------
        $save($canvas, $outputPath);

        foreach ([$base, $top, $bottom, $left, $right, $canvas] as $im) {
            if (is_resource($im) && $im instanceof \GdImage) {
                imagedestroy($im);
            }
        }

        return $outputPath;
    }
}
