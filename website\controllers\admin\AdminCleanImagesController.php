<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 *  <AUTHOR> bees <<EMAIL>>
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2017-2018 thirty bees
 *  @copyright 2007-2016 PrestaShop SA
 *  @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminMetaControllerCore
 *
 * @since 1.0.0
 */
class AdminCleanImagesControllerCore extends AdminController
{
    // @codingStandardsIgnoreStart
    public function __construct()
    {
        $this->bootstrap = true;
        $this->identifier_name = 'page';

        parent::__construct();
    }

    public function initContent () {
        setcookie('referer', $_SERVER['HTTP_REFERER'], time() + 3600, "/");
        header('Location: https://' . $_SERVER['SERVER_NAME'].'/img/img.php?refer=');
        //print_r($_SERVER['HTTP_REFERER']);
        exit;

    }
}