<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */
/**
 * Class AdminNewsletterControllerCore
 *
 * @since 1.0.0
 */
class AdminNewsletterTemplatesControllerCore extends AdminController
{

    public $name = 'newsletter_template';
    public $displayName = 'Newsletter Templates';

    public $filters = [];

    public function __construct() {

        $this->bootstrap = true;
        $this->table = 'newsletter_template';
        $this->className = 'NewsletterTemplate';
        $this->list_id = 'newsletter_template';

        $this->addRowAction('edit');
        $this->addRowAction('view');
        $this->addRowAction('delete');

        $this->db = Db::getInstance();
        $this->prefix = _DB_PREFIX_;

        parent::__construct();

        $this->display = 'list';

        $this->fields_list = [
            'id_newsletter_template' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'havingFilter' => true,
            ],

            'name' => [
                'title' => $this->l('Name'),
                'havingFilter' => true,
            ],
        ];

    }

    public function postProcess() {

        if(Tools::isSubmit('UploadImage')){
            $Uploader = new Uploader('files');

            $files = $Uploader->process();

            $data = [];

            foreach($files as $file) {
                $src = '/upload/' . $file['name'];

                $data = [
                    $src
                ];
            }

            echo Tools::jsonEncode([
                'data' => $data
            ]);

            exit;
        }

        parent::postProcess();

        if(Tools::isSubmit('content')){
            $this->db->update(
                'newsletter_template',

                [
                    'content' => Tools::getValue('content')
                ],

                'id_newsletter_template = \'' . $this->id_object . '\''
            );
        }

    }

    public function renderView() {

        return parent::renderView();

    }

    public function setCSS() {

        parent::setCSS();

        if ($this->display == 'add' || $this->display == 'edit') {
            $this->addCSS($this->theme_path . '/plugins/grapesjs/dist/css/grapes.min.css');
            // $this->addCSS($this->theme_path . '/plugins/grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.css');
            $this->addCSS($this->theme_path . '/plugins/grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.css');
            $this->addCSS($this->theme_path . '/css/newsletter_template.css', 'all');
        }

    }

    public function setJS(){
        
        parent::setJS();

        if ($this->display == 'add' || $this->display == 'edit') {
            $this->addJS($this->theme_path . '/plugins/grapesjs/dist/grapes.min.js');
            $this->addJS($this->theme_path . '/plugins/grapesjs-plugin-ckeditor/dist/grapesjs-plugin-ckeditor.min.js');
            $this->addJS($this->theme_path . '/plugins/grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.min.js');
            $this->addJS($this->theme_path . '/js/newsletter_template.js');

            $AssetImages = $this->GetAssetImages();

            Media::addJsDef([
                'AssetImages' => $AssetImages
            ]);
        }

    }

    public function GetAssetImages (){
        $UploadedImages = scandir(_PS_UPLOAD_DIR_);
        $AssetImages = [];

        foreach($UploadedImages as $Image)
            if(!is_dir($Image) && $Image != '.htaccess')
                $AssetImages[] = '/upload/' .$Image;

        return $AssetImages;
    }

    public function renderForm() {

        $this->fields_form = [
            'submit' => [
                'title' => $this->l('Save'),
            ],

            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'required' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'content',
                    'required' => true
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Style'),
                    'name' => 'style',
                    'required' => true
                ],
            ]
        ];

        return parent::renderForm();

    }

    public function renderList() {

        return parent::renderList();

    }
}
