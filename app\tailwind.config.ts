import type { Config } from "tailwindcss";

export default {
    content: [
        "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    ],
    theme: {
        extend: {
            fontFamily: {
                playfair: ['var(--font-playfair)'],
                poly: ['var(--font-poly)'],
            },

            colors: {
                background: "var(--background)",
                foreground: "var(--foreground)",
            },

            gridTemplateColumns: {
                'auto-2fr': 'auto auto',
                'auto-3fr': 'auto auto auto',
                'auto-4fr': 'auto auto auto auto',
            }
        },
    },
    plugins: [],
} satisfies Config;
