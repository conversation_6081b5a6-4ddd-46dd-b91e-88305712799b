<?php
/**
 * 2007-2016 PrestaShop
 *
 * thirty bees is an extension to the PrestaShop e-commerce software developed by PrestaShop SA
 * Copyright (C) 2017-2018 thirty bees
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.shoptech.media for more information.
 *
 * <AUTHOR> bees <<EMAIL>>
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2017-2018 thirty bees
 * @copyright 2007-2016 PrestaShop SA
 * @license   http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 *  PrestaShop is an internationally registered trademark & property of PrestaShop SA
 */

/**
 * Class AdminDashboardControllerCore
 *
 * @since 1.0.0
 */
class AdminProductAvailabilityControllerCore extends AdminDashboardController
{
    /**
     * AdminDashboardControllerCore constructor.
     *
     * @since 1.0.0
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @since 1.0.0
     */
    public function setMedia()
    {
        parent::setMedia();
    }

    /**
     * @since 1.0.0
     */
    public function initPageHeaderToolbar()
    {
        parent::initPageHeaderToolbar();

        unset($this->page_header_toolbar_btn['switch_demo']);
        unset($this->page_header_toolbar_btn['update_system']);
    }

    protected function setDashboard () {

        $products = (array) $this->getProducts();

        $total_quantities = 0;
        $total_price = 0;
        $average_price = 0;
        $total_value = 0;
        $total_wholesale = 0;

        foreach ($products as &$product) {
            $product['price'] = (float) Product::getPriceStatic($product['id_product']);
            $product['wholesale'] = (float) $product['wholesale_price'];
            $product['value'] = (float) $product['wholesale_price'] * $product['quantity'];

            $total_quantities += (int) $product['quantity'];
            $total_price += (float) $product['price'];
            $total_value += (float) $product['value'];
            $total_wholesale += (float) $product['wholesale_price'];
        }

        if ($total_quantities > 0 && $total_price > 0) {
            $average_price = (float) $total_price / $total_quantities;
        }

        $this->tpl_view_vars = [
            'date_from'          => $this->context->employee->stats_date_from,
            'date_to'            => $this->context->employee->stats_date_to,
            'preselect_date_range' => $this->context->employee->preselect_date_range,

            'products' => $products,
            'total_quantities' => $total_quantities,
            'total_price' => $total_price,
            'average_price' => $average_price,
            'total_value' => $total_value,
            'total_wholesale' => $total_wholesale,
        ];

    }

    private function getProducts () : array {

        $prefix = _DB_PREFIX_;

        $products = (array) Db::getInstance()->ExecuteS("
            SELECT p.id_product, pl.name, p.reference, sa.quantity, ps.wholesale_price, 0 as price, 0 as value FROM {$prefix}product as p

            INNER JOIN {$prefix}product_shop as ps ON (ps.id_product = p.id_product AND ps.id_shop = '{$this->context->shop->id}')
            INNER JOIN {$prefix}product_lang as pl ON (pl.id_product = p.id_product AND pl.id_lang = '{$this->context->language->id}' AND pl.id_shop = ps.id_shop)
            INNER JOIN {$prefix}stock_available as sa ON (sa.id_product = p.id_product AND sa.id_shop = ps.id_shop)

            GROUP BY p.id_product

            ORDER BY p.id_product ASC
        ");

        return $products;

    }

    /**
     * @return string
     *
     * @since 1.0.0
     */
    public function renderView()
    {
        return parent::renderView();
    }

    /**
     * @since 1.0.0
     */
    public function postProcess()
    {
        parent::postProcess();
    }
}