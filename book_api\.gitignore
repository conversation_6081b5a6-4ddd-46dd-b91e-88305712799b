### Symfony ###
/var/
/vendor/
/node_modules/
public/bundles/

# Environment files
.env
.env.local
.env.*.local

# SQLite database (if used)
*.sqlite
*.sqlite3

# Log files
*.log

# Cache files
.cache
.phpunit.result.cache

# IDE and OS-specific files
/.idea/
/.vscode/
/*.swp
.DS_Store
Thumbs.db

# Composer files
composer.lock

# Symfony Encore/Webpack
/public/build/
/public/hot
/public/assets/

# PHPUnit
/phpunit.xml
/phpunit.xml.dist

# Docker files
docker-compose.override.yml
docker-compose.local.yml

# Miscellaneous
.env.test.local
.env.dev.local
.env.prod.local
start

# Images
*.jpg
*.png
*.webp
