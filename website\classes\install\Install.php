<?php

/**
 * Methods used during System Setup and Installation
 */

class Install {

    public function __construct () {

    }

    public function init () {

        if ($this->isInstalled()) {

            Tools::redirect('/');
            exit;

        }

        if (Tools::isSubmit('setupInstall')) {

            $this->install();

        }

    }

    private function isInstalled () : bool {

        return (bool) Configuration::get('STM_SYSTEM_INSTALLED', false);

    }

    private function install () {

        $noindex = (bool) Tools::getValue('noindex', true);

        if ($noindex) {
            $this->addNoindex();
        }

        $this->setLogo('logo');

        $this->setStoreName(Tools::getValue('storeName'));

        $this->setStoreEmail(Tools::getValue('storeEmail'));

        $contactInformation = (array) Tools::getValue('contactInformation', []);

        $this->setContactInfo($contactInformation);

        $openingHours = (array) Tools::getValue('openingHours', []);

        $this->setOpeningHours($openingHours);

        $friendlyUrls = (array) Tools::getValue('friendlyUrls', []);

        $this->setFriendlyUrls($friendlyUrls);

        Configuration::updateValue('STM_SYSTEM_INSTALLED', true);

        Tools::redirect('/');
        exit;

    }

    private function addNoindex () {

        Configuration::updateValue('is_noindex', true);

    }

    private function setLogo (string $file) {

        if ($_FILES[$file]['size'] === 0)
            return false;

        $ext = end(explode('.', $_FILES[$file]['name']));

        $logoName = 'logo-' . time() . '.' . $ext;
        $logoDir = _PS_IMG_DIR_ . $logoName;

        $upload = move_uploaded_file($_FILES[$file]['tmp_name'], $logoDir);

        Configuration::updateValue('PS_LOGO', $logoName);

        // AdminThemesController::setLogo($file);

    }

    private function setStoreName (string $storeName) {

        Configuration::updateValue('PS_SHOP_NAME', $storeName);
        Configuration::updateValue('WKPOS_SHOP_NAME', $storeName);
        Configuration::updateValue('customcontactpage_COMPANY', $storeName);

    }

    private function setStoreEmail (string $storeEmail) {

        Configuration::updateValue('PS_SHOP_EMAIL', $storeEmail);

    }

    private function setContactInfo (array $contactInformation) {

        foreach ($contactInformation as $key => $value) {

            Configuration::updateValue($key, $value);

        }

    }

    private function setOpeningHours (array $openingHours) {

        Configuration::updateValue('OpeningHours', Tools::jsonEncode($openingHours));

    }

    private function setFriendlyUrls (array $friendlyUrls) {

        foreach ($friendlyUrls as $key => $friendlyUrl) {

            Configuration::updateValue('PS_ROUTE_' . $key, $friendlyUrl);

        }

    }

}