<?php

namespace App\Repository;

use App\Entity\ImageWebhook;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ImageWebhook>
 */
class ImageWebhookRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ImageWebhook::class);
    }

    /**
    * @return ImageWebhook[] Returns all image webhooks being processed
    */
    public function findProcessing(): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.is_processing = :is_processing')
            ->setParameter('is_processing', true)
            ->orderBy('b.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @return ImageWebhook[] Returns all unprocessed image webhooks
     */
    public function findUnprocessed(): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.is_processing = :is_processing')
            ->andWhere('b.is_processed = :is_processed')
            ->setParameter('is_processing', false)
            ->setParameter('is_processed', false)
            ->orderBy('b.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }
}
