<?php

namespace App\Repository;

use App\Entity\BookOrder;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<BookOrder>
 */
class BookOrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BookOrder::class);
    }

    /**
    * @return BookOrder[] Returns an array of BookImage objects
    */
    public function findByCartId(int $idCart): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.id_cart = :id_cart')
            ->setParameter('id_cart', $idCart)
            ->orderBy('b.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
    * @return BookOrder[] Returns an array of BookImage objects
    */
    public function findNotOrdered(): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.is_ordered = :is_ordered')
            ->setParameter('is_ordered', 0)
            ->orderBy('b.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
    * @return BookOrder[] Returns an array of BookImage objects
    */
    public function findReadyToOrder(): array
    {
        return $this->createQueryBuilder('b')
            ->andWhere('b.is_ordered = :is_ordered')
            ->andWhere('b.is_finished = :is_finished')
            ->setParameter('is_ordered', 1)
            ->setParameter('is_finished', 0)
            ->orderBy('b.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }
}
