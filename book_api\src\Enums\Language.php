<?php

namespace App\Enums;

enum Language: string
{
    case ENGLISH = 'en';
    case DANISH = 'da';

    /**
     * @param string $isoCode
     * 
     * @return Language
     */
    public static function getLanguage(string $isoCode) {
        foreach (Language::cases() as $language) {
            if ($language->value === $isoCode) {
                return $language;
            }
        }

        return Language::ENGLISH;
    }
}
