<?php

namespace App\Service;

use BenBjurstrom\Replicate\Replicate;

class ImageUpscaler
{
    private static function importAndSaveImage(string $imageUrl, string $savePath): array {
        // Initialize cURL session
        $ch = curl_init($imageUrl);
        
        // Set cURL options
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        // Execute cURL session
        $imageData = curl_exec($ch);
        
        // Check for errors
        if (curl_errno($ch)) {
            curl_close($ch);
            return ["success" => false, "error" => curl_error($ch)];
        }
        
        // Get HTTP response code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ["success" => false, "error" => "Failed to download image, HTTP Code: $httpCode"];
        }
        
        // Save the image to the specified path
        if (file_put_contents($savePath, $imageData)) {
            return ["success" => true, "message" => "Image saved successfully to $savePath"];
        } else {
            return ["success" => false, "error" => "Failed to save the image."];
        }
    }

    /**
     * Upscales images to 3 times its size
     * 
     * @param string $imagePath
     * @param string $outputPath
     * 
     * @return string Path to upscaled image
     */
    public static function upscale(string $imagePath, string $outputPath): string
    {
        $api = new Replicate(
            apiToken: $_ENV['REPLICATE_API_TOKEN'],
        );

        $imageName = basename($imagePath);
        $imageUrl = 'https://book.magipa.shoptech.media/public/generations/' . $imageName;

        $version = '4f7eb3da655b5182e559d50a0437440f242992d47e5e20bd82829a79dee61ff3';
        $input = [
            'model' => 'alexgenovese/upscaler',
            'image' => $imageUrl,
            'scale' => 3,
            'face_enhance' => true,
        ];

        $data = $api->predictions()->create($version, $input);

        $output = null;

        while ($output === null) {
            sleep(1);

            $data = $api->predictions()->get($data->id);

            $output = $data->output;

            if ($output) {
                self::importAndSaveImage($output, $outputPath);
                break;
            }
        }

        return $outputPath;
    }
}