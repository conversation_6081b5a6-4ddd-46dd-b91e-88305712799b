<?php

namespace App\Enums;

enum TitlePosition: int
{
    case TOP = 1;
    case CENTER = 2;
    case BOTTOM = 3;

    /**
     * @param int $position
     * 
     * @return TitlePosition
     */
    public static function getPosition(int $position) {
        foreach (TitlePosition::cases() as $titlePosition) {
            if ($titlePosition->value === $position) {
                return $titlePosition;
            }
        }

        return TitlePosition::CENTER;
    }
}
